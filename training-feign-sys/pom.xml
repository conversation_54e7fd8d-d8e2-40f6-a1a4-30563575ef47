<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
		<groupId>com.xunw</groupId>
		<artifactId>training-parent</artifactId>
		<version>5.3.0</version>
	</parent>

    <artifactId>training-feign-sys</artifactId>
    <packaging>jar</packaging>

    <description>用户权限管理系统公共模块</description>


    <dependencies>
        <!-- 连表查询注解 -->
        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join-annotation</artifactId>
        </dependency>
        <!--core 工具类-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-core</artifactId>
        </dependency>
        <!--mybatis plus extension,包含了mybatis plus core-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>
        <!--feign 工具类-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <!-- excel 导入导出 -->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-excel</artifactId>
        </dependency>
        <!--字段审计注解-->
        <dependency>
            <groupId>org.javers</groupId>
            <artifactId>javers-core</artifactId>
        </dependency>
         <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-swagger</artifactId>
        </dependency>
    </dependencies>
</project>
