/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.xunw.jxjy.feign.sys.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.xunw.jxjy.feign.sys.enums.OrgType;
import com.xunw.jxjy.feign.sys.enums.Status;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 租户
 *
 * <AUTHOR>
 * @date 2019-05-15 15:55:41
 */
@Data
@Schema(description = "租户信息")
@EqualsAndHashCode(callSuper = true)
@TableName("sys_org")
public class SysOrg extends Model<SysOrg> {

	private static final long serialVersionUID = 1L;

	/**
	 * 租户ID
	 */
	@TableId(type = IdType.ASSIGN_ID)
	@Schema(description = "租户ID")
	@ExcelProperty("租户ID")
	private String id;

	/**
	 * 租户编码
	 */
	@Schema(description = "租户编码")
	@ExcelProperty("租户编码")
	private String code;

	/**
	 * 租户名称
	 */
	@Schema(description = "租户名称")
	@ExcelProperty("租户名称")
	private String name;

	/**
	 * 组织类型
	 */
	@Schema(description = "机构类型")
	@ExcelProperty("机构类型")
	private OrgType orgType;

	/**
	 * 状态
	 */
	@Schema(description = "状态")
	@ExcelProperty("状态")
	private Status status;

	/**
	 * 锁定状态 0正常， 9锁定
	 */
	@Schema(description = "锁定状态")
	@ExcelProperty("状态")
	private String lockFlag;

	/**
	 * 父级ID
	 */
	@Schema(description = "父级ID")
	private String parentId;

	/**
	 * 是否为父级
	 */
	@Schema(description = "是否为父级")
	private String isParent;

	/**
	 * 备注
	 */
	@Schema(description = "备注")
	@ExcelProperty("备注")
	private String remark;

	/**
	 * 门户域名
	 */
	@Schema(description = "门户域名")
	@ExcelProperty("门户域名")
	private String portalDomain;

	/**
	 * 短信签名
	 */
	@Schema(description = "短信签名")
	@ExcelProperty("短信签名")
	private String smsSign;

	/**
	 * 是否定制化门户
	 */
	@Schema(description = "是否定制化门户")
	@ExcelProperty("是否定制化门户")
	private String isPortalCustomized;

	/**
	 * 管理域名
	 */
	@Schema(description = "管理域名")
	@ExcelProperty("管理域名")
	private String adminDomain;

	/**
	 * 管理系统名称
	 */
	@Schema(description = "管理系统名称")
	@ExcelProperty("管理系统名称")
	private String adminSysName;

	/**
	 * 门户系统名称
	 */
	@Schema(description = "门户系统名称")
	@ExcelProperty("门户系统名称")
	private String portalSysName;

	/**
	 * 省份代码
	 */
	@Schema(description = "省份代码")
	@ExcelProperty("省份代码")
	private String provinceCode;

	/**
	 * 城市代码
	 */
	@Schema(description = "城市代码")
	@ExcelProperty("城市代码")
	private String cityCode;

	/**
	 * 区县代码
	 */
	@Schema(description = "区县代码")
	@ExcelProperty("区县代码")
	private String districtCode;

	/**
	 * 是否集成CRP
	 */
	@Schema(description = "是否集成CRP")
	@ExcelProperty("是否集成CRP")
	private String isCrpIntegrate;

	/**
	 * 联系人
	 */
	@Schema(description = "联系人")
	@ExcelProperty("联系人")
	private String contact;

	/**
	 * 联系电话
	 */
	@Schema(description = "联系电话")
	@ExcelProperty("联系电话")
	private String telephone;

	/**
	 * 性质
	 */
	@Schema(description = "性质")
	@ExcelProperty("性质")
	private String nature;

	/**
	 * 管理端Logo
	 */
	@Schema(description = "管理端Logo")
	@ExcelProperty("管理端Logo")
	private String adminLogo;

	/**
	 * 门户Logo
	 */
	@Schema(description = "门户Logo")
	@ExcelProperty("门户Logo")
	private String portalLogo;

	/**
	 * iOS应用ID
	 */
	@Schema(description = "iOS应用ID")
	@ExcelProperty("iOS应用ID")
	private String iosAppid;

	/**
	 * 小程序二维码
	 */
	@Schema(description = "小程序二维码")
	@ExcelProperty("小程序二维码")
	private String appletQrCode;

	/**
	 * 是否为学院
	 */
	@Schema(description = "是否为学院")
	@ExcelProperty("是否为学院")
	private String isCollege;

	/**
	 * 创建人ID
	 */
	@Schema(description = "创建人ID")
	@TableField("creator_id")
	private String creatorId;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description = "创建时间")
	private LocalDateTime createTime;

	/**
	 * 修改人ID
	 */
	@Schema(description = "修改人ID")
	@TableField("updator_id")
	private String updatorId;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.UPDATE)
	@Schema(description = "更新时间")
	private LocalDateTime updateTime;

	/**
	 * 删除标志 0 未删除， 1已删除
	 */
	@Schema(description = "删除标志")
	private String delFlag;

	/**
	 * 菜单id
	 */
	@TableField(exist = false)
	private String MenuId;

	/**
	 * 部门
	 */
	@TableField(exist = false)
	private List<SysOrg> children;


	@TableField(exist = false)
	private String RoleType;

	/**
	 * 关键字
	 */
	@TableField(exist = false)
	private String keyword;

	/**
	 * 主办单位id
	 */
	@TableField(exist = false)
	private String hostOrgId;
}
