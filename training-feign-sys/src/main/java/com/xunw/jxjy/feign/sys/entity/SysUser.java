/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.xunw.jxjy.feign.sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xunw.jxjy.feign.sys.enums.Status;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.javers.core.metamodel.annotation.DiffInclude;
import org.javers.core.metamodel.annotation.PropertyName;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Data
@TableName("SYS_USER")
@Schema(description = "用户")
public class SysUser implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID 自增
	 */
	@TableId(type = IdType.ASSIGN_ID)
	@Schema(description = "主键id")
	private Long userId;

	/**
	 * 旧表的string类型用户id，不做主键防止影响框架，后续业务全部使用此id
	 */
	@TableField("id")
	@Schema(description = "旧表的string类型用户id，不做主键防止影响框架，后续业务全部使用此id")
	private String id;

	/**
	 * 用户名
	 */
	@TableField("USERNAME")
	@Schema(description = "用户名")
	private String username;

	/**
	 * 密码
	 */
	@TableField("PASSWORD")
	@Schema(description = "密码")
	private String password;

	/**
	 * 随机盐
	 */
	@JsonIgnore
	@TableField(exist = false)
	@Schema(description = "随机盐")
	private String salt;

	/**
	 * 0-正常，1-删除
	 */
	@TableLogic
	@TableField(value = "del_flag", fill = FieldFill.INSERT)
	@Schema(description = "删除标记,1:已删除,0:正常")
	private String delFlag;

	/**
	 * 状态
	 */
	@TableField("STATUS")
	@Schema(description = "状态")
	private Status status;

	/**
	 * 锁定标记 0未锁定 9已锁定
	 */
	@TableField("lock_flag")
	@Schema(description = "锁定标记 0未锁定 9已锁定")
	private String lockFlag;

	/**
	 * 姓名
	 */
	@TableField("NAME")
	@Schema(description = "姓名")
	private String name;

	/**
	 * 昵称
	 */
	@Schema(description = "昵称")
	private String nickname;

	/**
	 * 身份证号
	 */
	@DiffInclude
	@TableField("SFZH")
	@Schema(description = "身份证号")
	private String sfzh;

	/**
	 * 手机号
	 */
	@TableField("MOBILE")
	@DiffInclude
	@PropertyName("手机号")
	@Schema(description = "手机号")
	private String phone;

	/**
	 * 头像
	 */
	@TableField("AVATAR")
	@Schema(description = "头像地址")
	private String avatar;

	/**
	 * 用户所属租户id
	 */
	@TableField("ORG_ID")
	@Schema(description = "机构id，关联SYS_ORG表主键")
	private String orgId;

	/**
	 * 用户所属部门id
	 */
	@Schema(description = "用户所属部门id")
	private Long deptId;

	/**
	 * 微信openid
	 */
	@Schema(description = "微信openid")
	private String wxOpenid;

	/**
	 * 微信小程序openId
	 */
	@Schema(description = "微信小程序openid")
	private String miniOpenid;

	/**
	 * QQ openid
	 */
	@Schema(description = "QQ openid")
	private String qqOpenid;

	/**
	 * 码云唯一标识
	 */
	@Schema(description = "码云唯一标识")
	private String giteeLogin;

	/**
	 * 开源中国唯一标识
	 */
	@Schema(description = "开源中国唯一标识")
	private String oscId;

	/**
	 * 企微微信 userid
	 */
	@Schema(description = "企微微信 userid")
	private String wxCpUserid;

	/**
	 * 钉钉 userid
	 */
	@Schema(description = "钉钉 userid")
	private String wxDingUserid;

	/**
	 * 是否是CRP用户
	 */
	@TableField("IS_CRP_USER")
	@Schema(description = "是否是CRP用户")
	private String isCrpUser;

	/**
	 * 工号
	 */
	@TableField("EMPLOYEE_NUM")
	@Schema(description = "工号")
	private String employeeNum;

	/**
	 * 创建人
	 */
	@TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
	@Schema(description = "创建人")
	private String createBy;

	/**
	 * 修改人
	 */
	@TableField(value = "UPDATE_BY", fill = FieldFill.UPDATE)
	@Schema(description = "修改人")
	private String updateBy;

	/**
	 * 创建时间
	 */
	@TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
	@Schema(description = "创建时间")
	private LocalDateTime createTime;

	/**
	 * 修改时间
	 */
	@TableField(value = "UPDATE_TIME", fill = FieldFill.UPDATE)
	@Schema(description = "修改时间")
	private LocalDateTime updateTime;

	@TableField(exist = false)
	private String roleType;

	@TableField(exist = false)
	private Long roleId;
}
