package com.xunw.jxjy.feign.sys.enums;

/**
 * 专业层次
 *
 * <AUTHOR>
 */
public enum Edulevel {

	A("专科", "1"), B("专升本", "2"), C("高起本", "3");

	private String name;

	private String id;

	private Edulevel(String name, String id) {
		this.name = name;
		this.id = id;
	}

	public static Edulevel findById(String id) {
		for (Edulevel status : Edulevel.values()) {
			if (status.id.equals(id)) {
				return status;
			}
		}
		return null;
	}

	public static Edulevel findByName(String name) {
		for (Edulevel status : Edulevel.values()) {
			if (status.name.equals(name)) {
				return status;
			}
		}
		return null;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

}
