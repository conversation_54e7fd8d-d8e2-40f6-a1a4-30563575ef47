package com.xunw.jxjy.feign.sys.enums;

import com.baomidou.mybatisplus.annotation.IEnum;

import java.util.Objects;

public enum Status implements IEnum<String> {

    OK("开启", "0"),
    BLOCK("关闭", "1");

    private String name;
    private String id;

    Status(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public static Status findById(String id) {
        for (Status status : Status.values()) {
            if (Objects.equals(status.id, id)) {
                return status;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String getValue() {
        return this.name();
    }
}
