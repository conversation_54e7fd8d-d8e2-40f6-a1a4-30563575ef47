package com.xunw.jxjy.feign.sys.constant;


/**
 * 角色类型
 *
 * <AUTHOR>
 */
public enum RoleType {

    ADMIN("admin","超级管理员"),
    HOST_ORG("host_org","主办单位管理员"),
    HOST_ORG_CHILD("host_org_child","二级管理员"),
    ORG("org","培训机构管理员"),
    ENTRUST_ORG("entrust_org","委托单位管理员"),
    HEADERMASTER("headermaster","班主任"),
    XM_LEADER("xm_leader","项目负责人"),
    COLLEGE_LEADER("college_leader","院系负责人"),
    FINANCE("finance","财务"),
    TEACHER("teacher","讲师");

    private String id;

    private String name;

    RoleType(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public static RoleType findByEnumName(String name){
        for (RoleType role : RoleType.values()) {
            if (role.name().equals(name)) {
                return role;
            }
        }
        return null;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
