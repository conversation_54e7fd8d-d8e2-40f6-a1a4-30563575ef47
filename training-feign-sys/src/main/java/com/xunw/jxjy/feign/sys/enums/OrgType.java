package com.xunw.jxjy.feign.sys.enums;

import java.util.Objects;

public enum OrgType {
    HOST_ORG("主办单位", "host_org"),
    ORG("培训机构", "org"),
    ENTRUST_ORG("委托单位", "entrust_org"),
    KJDW("课件单位", "kjdw");

    private String name;
    private String id;

    OrgType(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public static OrgType findById(String id) {
        for (OrgType status : OrgType.values()) {
            if (Objects.equals(status.id, id)) {
                return status;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
