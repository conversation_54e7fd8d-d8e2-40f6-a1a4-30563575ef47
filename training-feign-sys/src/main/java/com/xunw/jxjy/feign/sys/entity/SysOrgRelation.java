/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.xunw.jxjy.feign.sys.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 机构关系表
 *
 * <AUTHOR>
 * @date 2019-05-15 15:55:41
 */
@Data
@Schema(description = "机构关系")
@EqualsAndHashCode(callSuper = true)
@TableName("sys_org_relation")
public class SysOrgRelation extends Model<SysOrgRelation> {

	private static final long serialVersionUID = 1L;

	/**
	 * 机构关系ID
	 */
	@TableId(type = IdType.ASSIGN_ID)
	@Schema(description = "机构关系ID")
	@ExcelProperty("机构关系ID")
	private String id;

	/**
	 * 主办单位ID
	 */
	@Schema(description = "主办单位ID")
	@ExcelProperty("主办单位ID")
	private String hostOrgId;

	/**
	 * 机构ID
	 */
	@Schema(description = "机构ID")
	@ExcelProperty("机构ID")
	private String orgId;
}
