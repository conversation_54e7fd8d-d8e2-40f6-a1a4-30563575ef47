/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.xunw.jxjy.feign.sys.vo;

import com.xunw.cloud.common.core.sensitive.Sensitive;
import com.xunw.cloud.common.core.sensitive.SensitiveTypeEnum;
import com.xunw.jxjy.feign.sys.entity.SysPost;
import com.xunw.jxjy.feign.sys.entity.SysRole;
import com.xunw.jxjy.feign.sys.enums.Education;
import com.xunw.jxjy.feign.sys.enums.Gender;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/10/29
 */
@Data
@Schema(description = "前端用户展示对象")
public class UserVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@Schema(description = "主键ID")
	private Long userId;

	/**
	 * 用户名
	 */
	@Schema(description = "用户名")
	private String username;

	/**
	 * 密码
	 */
	@Schema(description = "密码")
	private String password;

	/**
	 * 随机盐
	 */
	@Schema(description = "随机盐")
	private String salt;

	/**
	 * 微信openid
	 */
	@Schema(description = "微信openid")
	private String wxOpenid;

	/**
	 * QQ openid
	 */
	@Schema(description = "QQ openid")
	private String qqOpenid;

	/**
	 * gitee openid
	 */
	@Schema(description = "gitee openid")
	private String giteeOpenId;

	/**
	 * 开源中国 openid
	 */
	@Schema(description = "开源中国 openid")
	private String oscOpenId;

	/**
	 * 创建时间
	 */
	@Schema(description = "创建时间")
	private LocalDateTime createTime;

	/**
	 * 修改时间
	 */
	@Schema(description = "修改时间")
	private LocalDateTime updateTime;

	/**
	 * 0-正常，1-删除
	 */
	@Schema(description = "删除标记,1:已删除,0:正常")
	private String delFlag;

	/**
	 * 锁定标记
	 */
	@Schema(description = "锁定标记,0:正常,9:已锁定")
	private String lockFlag;

	/**
	 * 手机号
	 */
	@Sensitive(type = SensitiveTypeEnum.MOBILE_PHONE)
	@Schema(description = "手机号")
	private String phone;

	/**
	 * 头像
	 */
	@Schema(description = "头像")
	private String avatar;

	/**
	 * 部门ID
	 */
	@Schema(description = "所属部门ID")
	private Long deptId;

	/**
	 * 租户ID
	 */
	@Schema(description = "所属租户ID")
	private String tenantId;

	/**
	 * 部门名称
	 */
	@Schema(description = "所属部门名称")
	private String deptName;

	/**
	 * 角色列表
	 */
	@Schema(description = "拥有的角色列表")
	private List<SysRole> roleList;

	/**
	 * 岗位列表
	 */
	@Schema(description = "拥有的岗位列表")
	private List<SysPost> postList;

	/**
	 * 昵称
	 */
	@Schema(description = "昵称")
	private String nickname;

	/**
	 * 姓名
	 */
	@Schema(description = "姓名")
	private String name;

	/**
	 * 邮箱
	 */
	@Schema(description = "邮箱")
	private String email;

	/**
	 * 职务
	 */
	@Schema(description = "职务")
	private String zw;

	/**
	 * 人物简介
	 */
	@Schema(description = "人物简介")
	private String brief;

	/**
	 * 工作照片
	 */
	@Schema(description = "工作照片")
	private String photo;

	/**
	 * 职称
	 */
	@Schema(description = "职称")
	private String zc;

	/**
	 * 岗位
	 */
	@Schema(description = "岗位")
	private String gw;

	/**
	 * 学历
	 */
	@Schema(description = "学历")
	private Education education;

	/**
	 * 性别
	 */
	@Schema(description = "性别")
	private Gender gender;

	/**
	 * 工作单位
	 */
	@Schema(description = "工作单位")
	private String workUnit;

	/**
	 * 研究方向
	 */
	@Schema(description = "研究方向")
	private String studyDirection;

	/**
	 * 主讲课程
	 */
	@Schema(description = "主讲课程")
	private String courses;

	/**
	 * 办公电话
	 */
	@Schema(description = "办公电话")
	private String officeTel;

	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;

	/**
	 * 是否外聘，1是 0 否
	 */
	@Schema(description = "是否外聘，1是 0否")
	private String isOut;

	/**
	 * 推荐指数
	 */
	@Schema(description = "推荐指数")
	private Integer recommend;

	/**
	 * 自定义字段1
	 */
	@Schema(description = "自定义字段1")
	private String customOne;

	/**
	 * 自定义字段2
	 */
	@Schema(description = "自定义字段2")
	private String customTwo;

	/**
	 * 自定义字段3
	 */
	@Schema(description = "自定义字段3")
	private String customThree;

	/**
	 * 是否在门户上展示
	 */
	@Schema(description = "是否在门户上展示")
	private String isShowPortal;

	/**
	 * 职称证书扫描件
	 */
	@Schema(description = "职称证书扫描件")
	private String zcPhoto;

	/**
	 * 银行卡扫描件
	 */
	@Schema(description = "银行卡扫描件")
	private String bankCardPhoto;

	/**
	 * 类型id
	 */
	@Schema(description = "类型id")
	private String teacherTypeId;

	/**
	 * 专业类别
	 */
	@Schema(description = "专业类别")
	private String specialityType;

	/**
	 * 可评审工种
	 */
	@Schema(description = "可评审工种")
	private String professionIds;

	/**
	 * 银行卡号
	 */
	@Schema(description = "银行卡号")
	private String bankCardNo;

	/**
	 * 开户行
	 */
	@Schema(description = "开户行")
	private String bank;

	/**
	 * 校内：所属学院，校外：企业，高校，党政干部
	 */
	@Schema(description = "校内：所属学院，校外：企业，高校，党政干部")
	private String category;

	/**
	 * 身份证号
	 */
	@Schema(description = "身份证号")
	private String sfzh;
}
