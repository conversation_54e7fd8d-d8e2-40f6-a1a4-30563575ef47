/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 *
 */

package com.xunw.jxjy.feign.sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.xunw.jxjy.feign.sys.enums.Education;
import com.xunw.jxjy.feign.sys.enums.Gender;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 管理员用户信息表
 * </p>
 */
@Data
@Schema(description = "管理员用户信息")
@EqualsAndHashCode(callSuper = true)
@TableName("SYS_USER_INFO")
public class SysUserInfo extends Model<SysUserInfo> {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "ID", type = IdType.ASSIGN_ID)
	@Schema(description = "主键")
	private String id;

	/**
	 * 管理员用户表主键
	 */
	@TableField("USER_ID")
	@Schema(description = "管理员用户表主键")
	private String userId;

	/**
	 * 职务
	 */
	@TableField("ZW")
	@Schema(description = "职务")
	private String zw;

	/**
	 * 人物简介
	 */
	@TableField("BRIEF")
	@Schema(description = "人物简介")
	private String brief;

	/**
	 * 工作照片
	 */
	@TableField("PHOTO")
	@Schema(description = "工作照片")
	private String photo;

	/**
	 * 最后修改用户ID
	 */
	@TableField(value = "UPDATOR_ID", fill = FieldFill.UPDATE)
	@Schema(description = "最后修改用户ID")
	private String updatorId;

	/**
	 * 最后修改时间
	 */
	@TableField(value = "UPDATE_TIME", fill = FieldFill.UPDATE)
	@Schema(description = "最后修改时间")
	private LocalDateTime updateTime;

	/**
	 * 职称
	 */
	@TableField("ZC")
	@Schema(description = "职称")
	private String zc;

	/**
	 * 岗位
	 */
	@TableField("GW")
	@Schema(description = "岗位")
	private String gw;

	/**
	 * 创建时间
	 */
	@TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
	@Schema(description = "创建时间")
	private LocalDateTime createTime;

	/**
	 * 学历
	 */
	@TableField("EDUCATION")
	@Schema(description = "学历")
	private Education education;

	/**
	 * 创建用户ID
	 */
	@TableField(value = "CREATOR_ID", fill = FieldFill.INSERT)
	@Schema(description = "创建用户ID")
	private String creatorId;

	/**
	 * 性别
	 */
	@TableField("GENDER")
	@Schema(description = "性别")
	private Gender gender;

	/**
	 * 工作单位
	 */
	@TableField("WORK_UNIT")
	@Schema(description = "工作单位")
	private String workUnit;

	/**
	 * 研究方向
	 */
	@TableField("STUDY_DIRECTION")
	@Schema(description = "研究方向")
	private String studyDirection;

	/**
	 * 主讲课程
	 */
	@TableField("COURSES")
	@Schema(description = "主讲课程")
	private String courses;

	/**
	 * 办公电话
	 */
	@TableField("OFFICE_TEL")
	@Schema(description = "办公电话")
	private String officeTel;

	/**
	 * 备注
	 */
	@TableField("REMARK")
	@Schema(description = "备注")
	private String remark;

	/**
	 * 是否外聘，1是 0 否
	 */
	@TableField("IS_OUT")
	@Schema(description = "是否外聘，1是 0 否")
	private String isOut;

	/**
	 * 推荐指数
	 */
	@TableField("RECOMMEND")
	@Schema(description = "推荐指数")
	private Integer recommend;

	/**
	 * 自定义字段1
	 */
	@TableField("CUSTOM_ONE")
	@Schema(description = "自定义字段1")
	private String customOne;

	/**
	 * 自定义字段2
	 */
	@TableField("CUSTOM_TWO")
	@Schema(description = "自定义字段2")
	private String customTwo;

	/**
	 * 自定义字段3
	 */
	@TableField("CUSTOM_THREE")
	@Schema(description = "自定义字段3")
	private String customThree;

	/**
	 * 是否在门户上展示
	 */
	@TableField("IS_SHOW_PORTAL")
	@Schema(description = "是否在门户上展示")
	private String isShowPortal;

	/**
	 * 邮箱
	 */
	@TableField("EMAIL")
	@Schema(description = "邮箱")
	private String email;

	/**
	 * 职称证书扫描件
	 */
	@TableField("ZC_PHOTO")
	@Schema(description = "职称证书扫描件")
	private String zcPhoto;

	/**
	 * 银行卡扫描件
	 */
	@TableField("BANK_CARD_PHOTO")
	@Schema(description = "银行卡扫描件")
	private String bankCardPhoto;

	/**
	 * 类型id
	 */
	@TableField("TEACHER_TYPE_ID")
	@Schema(description = "类型id")
	private String teacherTypeId;

	/**
	 * 专业类别
	 */
	@TableField("SPECIALITY_TYPE")
	@Schema(description = "专业类别")
	private String specialityType;

	/**
	 * 可评审工种
	 */
	@TableField("PROFESSION_IDS")
	@Schema(description = "可评审工种")
	private String professionIds;

	/**
	 * 银行卡号
	 */
	@TableField("BANK_CARD_NO")
	@Schema(description = "银行卡号")
	private String bankCardNo;

	/**
	 * 开户行
	 */
	@TableField("BANK")
	@Schema(description = "开户行")
	private String bank;

	/**
	 * 校内：所属学院，校外：企业，高校，党政干部
	 */
	@TableField("CATEGORY")
	@Schema(description = "校内：所属学院，校外：企业，高校，党政干部")
	private String category;
}
