/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.xunw.jxjy.feign.sys.dto;

import com.xunw.jxjy.feign.sys.entity.SysUser;
import com.xunw.jxjy.feign.sys.enums.Education;
import com.xunw.jxjy.feign.sys.enums.Gender;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/11/5
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserDTO extends SysUser {

	/**
	 * 角色ID
	 */
	private List<Long> role;

	/**
	 * 岗位ID
	 */
	private List<Long> post;

	/**
	 * 新密码
	 */
	private String newpassword1;

	/**
	 * 锁定标记 0未锁定 9已锁定
	 */
	@NotBlank(message = "锁定标记不能为空")
	private String lockFlag;

	/**
	 * 职务
	 */
	private String zw;

	/**
	 * 人物简介
	 */
	private String brief;

	/**
	 * 工作照片
	 */
	private String photo;

	/**
	 * 职称
	 */
	private String zc;

	/**
	 * 岗位
	 */
	private String gw;

	/**
	 * 学历
	 */
	private Education education;

	/**
	 * 性别
	 */
	private Gender gender;

	/**
	 * 工作单位
	 */
	private String workUnit;

	/**
	 * 研究方向
	 */
	private String studyDirection;

	/**
	 * 主讲课程
	 */
	private String courses;

	/**
	 * 办公电话
	 */
	private String officeTel;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 是否外聘，1是 0 否
	 */
	private String isOut;

	/**
	 * 推荐指数
	 */
	private Integer recommend;

	/**
	 * 自定义字段1
	 */
	private String customOne;

	/**
	 * 自定义字段2
	 */
	private String customTwo;

	/**
	 * 自定义字段3
	 */
	private String customThree;

	/**
	 * 是否在门户上展示
	 */
	private String isShowPortal;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 职称证书扫描件
	 */
	private String zcPhoto;

	/**
	 * 银行卡扫描件
	 */
	private String bankCardPhoto;

	/**
	 * 类型id
	 */
	private String teacherTypeId;

	/**
	 * 专业类别
	 */
	private String specialityType;

	/**
	 * 可评审工种
	 */
	private String professionIds;

	/**
	 * 银行卡号
	 */
	private String bankCardNo;

	/**
	 * 开户行
	 */
	private String bank;

	/**
	 * 校内：所属学院，校外：企业，高校，党政干部
	 */
	private String category;

	/**
	 * 主办单位id
	 */
	private String tenantId;
}
