package com.xunw.jxjy.feign.sys.enums;

/**
 * 学历
 */
public enum Education {
    BOSHI("博士", "1"),//博士,
    SHUOSHI("硕士", "2"),//硕士
    YANJIUSHENG("研究生", "3"),//研究生及以上
    BENKE("本科", "4"),//大学本科
    DAZHUNAN("大专", "5"),//大专
    ZHONGZHUAN("中专", "6"),//中专
    GAOJIJIXIAO("高级技校", "7"),//高级技校
    JIXIAO("技校", "8"),//技校
    JISHIXUEYUAN("技师学院", "9"),//技师院校
    ZHIGAO("职高", "10"),//职高
    GAOZHONG("高中", "11"),//高中
    CHUZHONG("初中", "12"),//初中
    XIAOXUE("小学", "13");//小学

    private String name;

    private String id;

    private Education(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
