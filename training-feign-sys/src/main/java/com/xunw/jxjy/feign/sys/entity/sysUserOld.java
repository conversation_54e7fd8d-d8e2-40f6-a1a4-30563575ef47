package com.xunw.jxjy.feign.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统用户表
 * <AUTHOR>
 */
@TableName("sys_user_old")
@Data
public class sysUserOld implements Serializable {

    private static final long serialVersionUID = 1863546633205455112L;

    //id
    @TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;

    //用户名
    @TableField("username")
    private String username;

    //密码
    @TableField("password")
    private String password;

    //姓名
    @TableField("name")
    private String name;

    //身份证号
    @TableField("sfzh")
    private String sfzh;

    //手机号
    @TableField("mobile")
    private String mobile;

    //机构id
    @TableField("org_id")
    private String orgId;

    //状态
    @TableField("status")
    private String status;

    //创建用户id
    @TableField("creator_id")
    private String creatorId;

    //创建时间
    @TableField("create_time")
    private Date createTime;

    //修改用户id
    @TableField("updator_id")
    private String updatorId;

    //修改时间
    @TableField("update_time")
    private Date updateTime;

    //个人头像
    @TableField("avatar")
    private String avatar;

    //是否是CRP用户 1是 0 否
    @TableField("is_crp_user")
    private String isCrpUser;

    //工号
    @TableField("EMPLOYEE_NUM")
    private String employeeNum;
}
