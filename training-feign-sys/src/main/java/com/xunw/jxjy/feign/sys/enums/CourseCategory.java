package com.xunw.jxjy.feign.sys.enums;


/**
 * 课程类别
 */
public enum CourseCategory {

    COMBASE("公共基础课","1"),

    SPECCORE("专业核心课","2"),

    SPECEXPAND("专业拓展课","3"),

    GRADUATION("毕业考核课","4");

    private String name;

    private String id;

    private CourseCategory(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public static CourseCategory findById(String id) {
        for (CourseCategory status : CourseCategory.values()) {
            if (status.id.equals(id)) {
                return status;
            }
        }
        return null;
    }

    public static CourseCategory findByName(String name) {
        for (CourseCategory status : CourseCategory.values()) {
            if (status.name.equals(name)) {
                return status;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

}
