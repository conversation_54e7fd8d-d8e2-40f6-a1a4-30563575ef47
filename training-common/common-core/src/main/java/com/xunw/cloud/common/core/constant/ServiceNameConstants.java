/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.xunw.cloud.common.core.constant;

/**
 * <AUTHOR>
 * @date 2018年06月22日16:41:01 服务名称
 */
public interface ServiceNameConstants {

	/**
	 * UMPS模块
	 */
	String UPMS_SERVICE = "training-service";


	/**
	 * app服务
	 */
	String APP_SERVER = "xunw-cloud-app-server-biz";

	/**
	 * 流程引擎
	 */
	String FLOW_ENGINE_SERVER = "xunw-cloud-flow-engine-biz";

	/**
	 * 流程工单
	 */
	String FLOW_TASK_SERVER = "xunw-cloud-flow-task-biz";

	/**
	 * JXJY业务模块
	 */
	String JXJY_SERVICE = "xunw-cloud-jxjy-biz";

}
