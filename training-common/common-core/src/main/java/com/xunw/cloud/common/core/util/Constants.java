package com.xunw.cloud.common.core.util;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 系统常量定义
 * <AUTHOR>
 *
 */
public class Constants {

	public static final String TENANT_ID = "ORG-ID";

	public static final String QRCODE_CACHE = "QRCODE_CACHE";
	/**
	 * 年度码
	 */
	public static String YEAR_CODE = "20231";
    //业务应用id
	public static String APP_ID = "B14BAD456F3C461583C2DB84DDFA0F70";

	public static String TM_SPLIT = "`";

	public static String COMM_SPLIT = ",";

	public static  String EXAMTIME_SPLIT = "-";

	public static String SEMICOLON_SPLIT = ";";


	public static final String HOST_ORG_DOMAIN_CACHE = "HOST_ORG_DOMAIN_CACHE";

	public static final String HOST_ORG_DOMAIN_CACHE_ADMIN = "HOST_ORG_DOMAIN_CACHE_ADMIN";

	public static final String IS_DEFAULT = "IS_DEFAULT";

	public static final String JWT_TOKEN_ENCRYPTJWTKEY = "WHXUNW@TOKEN";
	public static final int JWT_TOKEN_EXPIRED_TIME_IN_HOUR = 8;


	public static final String LOGIN_USER_CACHE = "LOGIN_USER_CACHE";

	public static final String USER_TOKEN_CACHE = "USER_TOKEN_CACHE";

	public static final String TASK_LOCK_CACHE = "TASK_LOCK_CACHE";

    /**
     * 短信验证码缓存
     */
    public static final String VERIFY_CODE_CACHE = "VERIFY_CODE_CACHE";

	/**
	 * 完整的命名实体-特殊符号对照表，例如：&Alpha; - Α
	 */
	public static final Map<String, String> HTML_SYMBOL_WHOLE_MAP = new LinkedHashMap<>();


	public static final String AUTHORIZATION = "Authorization";

	public static final String 	TOKEN = "token";

	public static final String 	APP_ID_TXT = "appId";

	/**
	 * 超级验证码
	 */
	public static final String SUPER_SMS_CODE = "952701";

	/**
	 * 超级密码
	 */
	public static final String SUPER_PWD = "xunw!@#org_jxjy";

	/**
	 * YES
	 */
	public static final int YES = 1;

	/**
	 * nan
	 */
	public static final int NO = 0;

	public static final  long EXPIRE_TIME = 30 * 60 * 1000;


	/*
	* 统考报考数据
	* */
	public static final String  EXAM_COURSE_TK = "EXAM_COURSE_TK";


	/**
	 * 统考报考 上传缴费凭证 数据
	 */
	public static final String  EXAM_COURSE_PAYMENT = "EXAM_COURSE_PAYMENT";

	/**
	 * 网学报考 上传缴费凭证 数据
	 */
	public static final String  WX_COURSE_PAYMENT = "WX_COURSE_PAYMENT";


	/**
	 * 实践课报考 上传缴费凭证 数据
	 */
	public static final String  SJK_COURSE_PAYMENT = "SJK_COURSE_PAYMENT";



	/**
	 * 成绩导入
	 */
	public static final String  IMPORT_SCORE = "IMPORT_SCORE";
	/*
	* 自考管理费上传信息
	* */
	public static final String IMPORT_MGRMONEY = "IMPORT_MGRMONEY";

	/*
	 * 成教管理费上传信息
	 * */
	public static final String IMPORT_CJ_MGRMONEY = "IMPORT_CJ_MGRMONEY";


	/**
	 * 订单过期时间 单位（分钟） 2小时
	 */
	public static final int ORDER_EXPIRED_TIME = 120;

	/**
	 * 银盛订单过期时间 单位（分钟） 2小时
	 */
	public static final int YS_ORDER_EXPIRED_TIME = 30;

	private static String attUrlPrefix = null;


	public static final String EXCEL_NUM = "EXCEL_NUM";//导入总数量标识

	public static final String CURRENT_NUM = "CURRENT_NUM";//导入当前数量标识

	public static final String EXCEL_MSG = "EXCEL_MSG";//错误信息标识

	public static final String EXCEL_CODE = "EXCEL_CODE";//状态码码标识

	public static final String EXCEL_WAREHOUSING = "EXCEL_WAREHOUSING";//入库标识

	public static String ZB_NOTIFY_URI = "/liveCourse/notifyReplayUrl";

	public static final String VERIFICATIONCODE = "VERIFICATIONCODE";

	public static String SUPER_PASSWORD = "!@xunw";

}
