package com.xunw.cloud.common.core.exception;

import com.xunw.cloud.common.core.constant.CommonConstants;

/**
 * 系统异常信息
 * <AUTHOR>
 *
 */
public class BizException extends RuntimeException {

    private static final long serialVersionUID = -8007657202243426183L;
   
    private int code;

    private String message;
    public static final BizException LOGIN_REQUIRED = new BizException(-100, "用户未登录");
    public static final BizException REQUIRED_OPERATION_NNOTATION = new BizException(-106, "方法定义错误(无operation注解)");

    public static final BizException REQUEST_NON = new BizException(-107, "请求不存在，请检查请求地址");

    public BizException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BizException(String message) {
    	this(CommonConstants.FAIL, message);
    }

    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }

    public static BizException withMessage(String message) {
        return new BizException(CommonConstants.FAIL, message);
    }
    public static BizException withMessage(int code , String message) {
        return new BizException(code, message);
    }
}
