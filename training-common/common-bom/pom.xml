<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>group.springframework</groupId>
        <artifactId>spring-cloud-dependencies-parent</artifactId>
        <version>2021.0.7</version>
        <relativePath/>
    </parent>

    <groupId>com.xunw</groupId>
    <artifactId>common-bom</artifactId>
    <packaging>pom</packaging>
    <version>${pigx.version}</version>
    <description>pigx 公共版本控制</description>

    <properties>
        <pigx.version>5.3.0</pigx.version>
        <mybatis-plus.version>3.5.4</mybatis-plus.version>
        <mybatis-plus-join.version>1.4.6</mybatis-plus-join.version>
        <dynamic-ds.version>4.2.0</dynamic-ds.version>
        <druid.version>1.2.20</druid.version>
        <hutool.version>5.8.22</hutool.version>
        <mysql.connector.version>8.0.33</mysql.connector.version>
        <oracle.version>********</oracle.version>
        <sqlserver.version>8.4.1.jre8</sqlserver.version>
        <dm.version>*********</dm.version>
        <highgo.version>6.2.0</highgo.version>
        <knife4j.version>3.0.3</knife4j.version>
        <swagger.core.version>2.2.8</swagger.core.version>
        <springdoc.version>1.6.15</springdoc.version>
        <mp.weixin.version>4.4.0</mp.weixin.version>
        <ijpay.version>2.8.0</ijpay.version>
        <groovy.version>3.0.3</groovy.version>
        <javax.version>4.0.1</javax.version>
        <jsoup.version>1.13.1</jsoup.version>
        <aviator.version>5.3.3</aviator.version>
        <flowable.version>6.8.0</flowable.version>
        <security.oauth.version>2.5.2.RELEASE</security.oauth.version>
        <fastjson.version>1.2.83</fastjson.version>
        <xxl.job.version>2.3.0</xxl.job.version>
        <aliyun.version>3.0.52.ALL</aliyun.version>
        <aws.version>1.12.261</aws.version>
        <javers.version>6.10.0</javers.version>
        <seata.version>1.6.1</seata.version>
        <asm.version>7.1</asm.version>
        <log4j2.version>2.17.1</log4j2.version>
        <javaformat.plugin.version>0.0.23</javaformat.plugin.version>
        <docker.plugin.version>0.33.0</docker.plugin.version>
        <cloud.plugin.version>1.0.0</cloud.plugin.version>
        <sentinel.version>1.8.4</sentinel.version>
        <jwt.version>4.2.1</jwt.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-core</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-audit</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-data</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-gateway</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-gray</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-idempotent</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-job</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-log</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-oss</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-security</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-sentinel</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-feign</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-sequence</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-swagger</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-seata</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-xss</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-websocket</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-encrypt-api</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>common-excel</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>training-feign-sys</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xunw</groupId>
                <artifactId>training-flow</artifactId>
                <version>${pigx.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>
            <!--  seata kryo 序列化-->
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-serializer-kryo</artifactId>
                <version>${seata.version}</version>
            </dependency>
            <!--mybatis plus extension,包含了mybatis plus core-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!--mybatis-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>
            <!-- 连表查询依赖	-->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <!-- 连表查询依赖	-->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-annotation</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <!-- druid 连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!--mysql 驱动-->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <!--oracle 驱动-->
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle.version}</version>
            </dependency>
            <!-- mssql -->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${sqlserver.version}</version>
            </dependency>
            <!--DM8-->
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm.version}</version>
            </dependency>
            <!--fastjson-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!-- 对象对比工具-->
            <dependency>
                <groupId>org.javers</groupId>
                <artifactId>javers-core</artifactId>
                <version>${javers.version}</version>
            </dependency>
            <!--springdoc -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webmvc-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springboot</groupId>
                <artifactId>knife4j-openapi3-ui</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webflux-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-common</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-security</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${mp.weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-common</artifactId>
                <version>${mp.weixin.version}</version>
            </dependency>
            <!--计算引擎-->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.version}</version>
            </dependency>
            <!--jsoup html 解析组件-->
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <!--  指定 log4j 版本-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>${log4j2.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--hutool bom-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-web-servlet</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-transport-simple-http</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-parameter-flow-control</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-api-gateway-adapter-common</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- jwt -->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <!--  增加云效nexus示例仓库 （演示使用，可自行删除）  -->
    <distributionManagement>
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2161442-release-DcBZC1/</url>
        </repository>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://packages.aliyun.com/maven/repository/2161442-snapshot-FzKqZK/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
