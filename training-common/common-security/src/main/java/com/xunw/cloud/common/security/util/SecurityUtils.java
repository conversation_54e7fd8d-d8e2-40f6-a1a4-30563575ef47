/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.xunw.cloud.common.security.util;

import cn.hutool.core.util.StrUtil;

import com.xunw.cloud.common.core.constant.SecurityConstants;
import com.xunw.cloud.common.security.service.PigxUser;

import lombok.experimental.UtilityClass;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 安全工具类
 *
 * <AUTHOR>
 */
@UtilityClass
public class SecurityUtils {

	/**
	 * 获取Authentication
	 */
	public Authentication getAuthentication() {
		return SecurityContextHolder.getContext().getAuthentication();
	}

	/**
	 * 获取用户
	 * @param authentication
	 * @return PigxUser
	 * <p>
	 */
	public PigxUser getUser(Authentication authentication) {
		Object principal = authentication.getPrincipal();
		if (principal instanceof PigxUser) {
			return (PigxUser) principal;
		}
		return null;
	}

	public String getRoleType() {
		Authentication authentication = getAuthentication();
		PigxUser pigxUser = getUser(authentication);
		return pigxUser.getRoleType();
	}

	public void setRoleType(String roleType) {
		Authentication authentication = getAuthentication();
		PigxUser pigxUser = getUser(authentication);
		pigxUser.setRoleType(roleType);
		Authentication newAuth = new UsernamePasswordAuthenticationToken(pigxUser, pigxUser.getPassword(), pigxUser.getAuthorities());
		SecurityContextHolder.getContext().setAuthentication(newAuth);
	}

	/**
	 * 获取用户
	 */
	public PigxUser getUser() {
		Authentication authentication = getAuthentication();
		return getUser(authentication);
	}

	/**
	 * 获取租户id
	 */
	public String getTenantId() {
		Authentication authentication = getAuthentication();
		return getUser(authentication).getTenantId();
	}

	/**
	 * 获取用户角色信息
	 * @return 角色集合
	 */
	public List<Long> getRoles() {
		Authentication authentication = getAuthentication();
		Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();

		List<Long> roleIds = new ArrayList<>();
		authorities.stream().filter(granted -> StrUtil.startWith(granted.getAuthority(), SecurityConstants.ROLE))
				.forEach(granted -> {
					String id = StrUtil.removePrefix(granted.getAuthority(), SecurityConstants.ROLE);
					roleIds.add(Long.parseLong(id));
				});
		return roleIds;
	}

}
