package com.xunw.cloud.common.security.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.xunw.cloud.common.core.exception.BizException;
import com.xunw.cloud.common.core.util.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JWTUtils {

	private static final Logger LOGGER = LoggerFactory.getLogger(JWTUtils.class);

	 /**
     * 校验token是否正确
     * @param token Token
     * @return boolean 是否正确
     * <AUTHOR>
     * @date 2018/8/31 9:05
     */
    public static boolean verify(String token) {
        if (token.startsWith("Bearer")) {
            token = token.split(" ")[1];
        }
        try {
            // 帐号加JWT私钥解密
            String secret = getClaim(token, "userId")
                    + getClaim(token, "username")
                    + getClaim(token, "userType")
                    + getClaim(token, "appId")
                    + Constants.JWT_TOKEN_ENCRYPTJWTKEY;
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .build();
            DecodedJWT jwt = verifier.verify(token);
            return true;
        } catch (Exception e) {
            LOGGER.warn("JWTToken认证失败:"+e+"");
            return false;
        }
    }

    /**
     * 获得Token中的信息无需secret解密也能获得
     * @param token
     * @param claim
     * @return java.lang.String
     * <AUTHOR>
     * @date 2018/9/7 16:54
     */
    public static String getClaim(String token, String claim) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            // 只能输出String类型，如果是其他类型返回null
            return jwt.getClaim(claim).asString();
        } catch (JWTDecodeException e) {
            e.printStackTrace();
            throw BizException.withMessage("解密Token中的公共信息出现JWTDecodeException异常:" + e);
        }
    }

    /**
     * 生成签名
     * @param
     * @return java.lang.String 返回加密的Token
     * <AUTHOR>
     * @date 2018/8/31 9:07
     */
    public static String sign(String userId, String username, String userType,String orgId, String appId) {
        try {
            // 帐号加JWT私钥加密
            String secret = userId + username + userType + appId + Constants.JWT_TOKEN_ENCRYPTJWTKEY;
            // 此处过期时间是以毫秒为单位，所以乘以1000
//            Date date = new Date(System.currentTimeMillis() + Constants.JWT_TOKEN_EXPIRED_TIME_IN_HOUR * 3600*1000);
            Algorithm algorithm = Algorithm.HMAC256(secret);
            // 附带account帐号信息
            return JWT.create()
                    .withClaim("userId", userId)
                    .withClaim("username", username)
                    .withClaim("userType", userType)
                    .withClaim("appId", appId)
                    .withClaim("orgId", orgId)
                    .withClaim("timestamp", String.valueOf(System.currentTimeMillis()))
//                    .withExpiresAt(date)
                    .sign(algorithm);
        } catch (IllegalArgumentException e) {
            throw BizException.withMessage("JWTToken加密出现IllegalArgumentException异常:"+e);
        } catch (JWTCreationException e) {
            throw BizException.withMessage("JWTToken加密出现JWTCreationException异常:"+e);
        }
    }
}
