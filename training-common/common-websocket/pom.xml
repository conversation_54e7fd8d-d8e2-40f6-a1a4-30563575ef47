<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.xunw</groupId>
		<artifactId>training-common</artifactId>
		<version>5.3.0</version>
	</parent>

	<artifactId>common-websocket</artifactId>
	<packaging>jar</packaging>

	<description>pigx websocket</description>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-json</artifactId>
		</dependency>

		<dependency>
			<groupId>com.xunw</groupId>
			<artifactId>common-security</artifactId>
		</dependency>
	</dependencies>
</project>
