<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: lengleng (<EMAIL>)
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.xunw</groupId>
		<artifactId>training-parent</artifactId>
		<version>5.3.0</version>
	</parent>

	<artifactId>training-common</artifactId>
	<packaging>pom</packaging>

	<description>pigx 公共聚合模块</description>

	<modules>
		<module>common-audit</module>
		<module>common-bom</module>
		<module>common-core</module>
		<module>common-data</module>
		<module>common-excel</module>
		<module>common-encrypt-api</module>
		<module>common-feign</module>
		<module>common-gateway</module>
		<module>common-gray</module>
		<module>common-idempotent</module>
		<module>common-job</module>
		<module>common-log</module>
		<module>common-oss</module>
		<module>common-seata</module>
		<module>common-security</module>
		<module>common-sentinel</module>
		<module>common-sequence</module>
		<module>common-swagger</module>
		<module>common-websocket</module>
		<module>common-xss</module>
	</modules>
</project>
