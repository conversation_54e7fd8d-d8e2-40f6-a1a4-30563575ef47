<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: lengleng (<EMAIL>)
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xunw</groupId>
        <artifactId>training-parent</artifactId>
        <version>5.3.0</version>
    </parent>

    <artifactId>training-flow</artifactId>
    <description>pigx 工作流</description>
    <packaging>jar</packaging>

    <dependencies>
        <!--core 工具类-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-core</artifactId>
        </dependency>
        <!--mybatis plus extension,包含了mybatis plus core-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>
        <!--feign 工具类-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-feign</artifactId>
        </dependency>
        <!-- excel 导入导出 -->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-excel</artifactId>
        </dependency>

        <!--必备: 操作数据源相关-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-data</artifactId>
        </dependency>
        <!--选配： 发号器-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-sequence</artifactId>
        </dependency>
        <!--必备：pigx安全模块-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-security</artifactId>
        </dependency>
        <!--必备：xss 过滤模块-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-xss</artifactId>
        </dependency>
        <!--必备: log 依赖-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-log</artifactId>
        </dependency>
        <!--选配: mybatis 依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <!-- flowable 核心依赖-->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-bpmn-layout</artifactId>
            <version>${flowable.version}</version>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-process</artifactId>
            <version>${flowable.version}</version>
        </dependency>
        <!--计算引擎-->
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.imports</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.imports</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
