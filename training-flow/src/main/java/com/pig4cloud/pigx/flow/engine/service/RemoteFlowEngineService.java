package com.pig4cloud.pigx.flow.engine.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pig4cloud.pigx.flow.engine.utils.ModelUtil;
import com.pig4cloud.pigx.flow.task.dto.*;
import com.pig4cloud.pigx.flow.task.entity.Process;
import com.pig4cloud.pigx.flow.task.service.ITaskService;
import com.pig4cloud.pigx.flow.task.utils.NodeUtil;
import com.xunw.cloud.common.core.util.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricActivityInstanceQuery;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description
 * <AUTHOR>
 * @date 2025/4/9 15:59
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RemoteFlowEngineService {

    private final TaskService taskService;

    private final ITaskService iTaskService;

    private final HistoryService historyService;

    private final RuntimeService runtimeService;

    private final RepositoryService repositoryService;

    private final ObjectMapper objectMapper;

    /**
     * 查询任务变量
     * @param variableQueryParamDto 变量查询参数
     * @return 任务变量结果
     */
    public R<Map<String, Object>> queryTaskVariables(VariableQueryParamDto variableQueryParamDto) {
        List<String> keyList = variableQueryParamDto.getKeyList();
        if (CollUtil.isEmpty(keyList)) {
            TaskQuery taskQuery = taskService.createTaskQuery();

            Task task = taskQuery.taskId(variableQueryParamDto.getTaskId()).singleResult();
            if (task == null) {
                return R.failed("任务不存在");
            }

            Map<String, Object> variables = runtimeService.getVariables(task.getExecutionId());

            return R.ok(variables);
        }
        Map<String, Object> variables = taskService.getVariables(variableQueryParamDto.getTaskId(), keyList);
        return R.ok(variables);
    }

    /**
     * 查询简单数据
     * @param userId 用户ID
     * @return 索引页统计数据
     */
    public R<IndexPageStatistics> querySimpleData(Long userId) {
        TaskQuery taskQuery = taskService.createTaskQuery();

        // 待办数量
        long pendingNum = taskQuery.taskAssignee(String.valueOf(userId)).count();
        // 已完成任务
        HistoricActivityInstanceQuery historicActivityInstanceQuery = historyService
                .createHistoricActivityInstanceQuery();

        long completedNum = historicActivityInstanceQuery.taskAssignee(String.valueOf(userId)).finished().count();

        IndexPageStatistics indexPageStatistics = IndexPageStatistics.builder().pendingNum(pendingNum)
                .completedNum(completedNum).build();

        return R.ok(indexPageStatistics);
    }

    /**
     * 创建流程
     * @param map 流程JSON对象
     * @return 创建流程结果
     */
    public R<String> createFlow(Map<String, Object> map) throws JsonProcessingException {

        Process process = MapUtil.get(map, "process", Process.class);

        String flowId = "P" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN)
                + RandomUtil.randomString(5).toUpperCase();
        log.info("flowId={}", flowId);
        BpmnModel bpmnModel = ModelUtil.buildBpmnModel(objectMapper.readValue(process.getProcess(), Node.class),
                process.getName(), flowId);
        {
            byte[] bpmnBytess = new BpmnXMLConverter().convertToXML(bpmnModel);
            String filename = "/tmp/flowable-deployment/" + flowId + ".bpmn20.xml";
            log.debug("部署时的模型文件：{}", filename);
            FileUtil.writeBytes(bpmnBytess, filename);
        }
        repositoryService.createDeployment().addBpmnModel(StrUtil.format("{}.bpmn20.xml", "pig"), bpmnModel).deploy();

        return R.ok(flowId);
    }

    /**
     * 启动流程实例
     * @param processInstanceParamDto 流程实例参数
     * @return 启动流程结果
     */
    public R<String> startProcess(ProcessInstanceParamDto processInstanceParamDto) {
        Authentication.setAuthenticatedUserId(processInstanceParamDto.getStartUserId());
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processInstanceParamDto.getFlowId(),
                processInstanceParamDto.getParamMap());

        String processInstanceId = processInstance.getProcessInstanceId();
        return R.ok(processInstanceId);
    }

    /**
     * 查询待办任务
     * @param taskQueryParamDto 任务查询参数
     * @return 待办任务列表
     */
    public R<Page<TaskDto>> queryAssignTask(TaskQueryParamDto taskQueryParamDto) {
        String assign = taskQueryParamDto.getAssign();

        List<TaskDto> taskDtoList = new ArrayList<>();

        int pageIndex = taskQueryParamDto.getPageNum() - 1;
        int pageSize = taskQueryParamDto.getPageSize();

        Page<TaskDto> pageResultDto = new Page<>();
        TaskQuery taskQuery = taskService.createTaskQuery().taskAssignee(assign).orderByTaskCreateTime().desc();
        if (StrUtil.isNotBlank(taskQueryParamDto.getProcessName())) {
            List<ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery()
                    .processInstanceNameLikeIgnoreCase(taskQueryParamDto.getProcessName()).list();
            if (CollUtil.isNotEmpty(processInstanceList)) {
                taskQuery.processInstanceIdIn(processInstanceList.stream().map(ProcessInstance::getProcessDefinitionId)
                        .collect(Collectors.toList()));
            }
        }

        if (ArrayUtil.isNotEmpty(taskQueryParamDto.getTaskTime())) {
            ZoneId zoneId = ZoneId.systemDefault();
            ZonedDateTime zonedBeforeDateTime = taskQueryParamDto.getTaskTime()[0].atZone(zoneId);
            ZonedDateTime zonedAfterDateTime = taskQueryParamDto.getTaskTime()[1].atZone(zoneId);
            Date beforeDate = Date.from(zonedBeforeDateTime.toInstant());
            Date afterDate = Date.from(zonedAfterDateTime.toInstant());
            taskQuery.taskCreatedBefore(afterDate).taskCreatedAfter(beforeDate);
        }

        taskQuery.listPage(pageIndex * pageSize, pageSize).forEach(task -> {
            String taskId = task.getId();
            String processInstanceId = task.getProcessInstanceId();
            log.debug("(taskId) " + task.getName() + " processInstanceId={} executrionId={}", processInstanceId,
                    task.getExecutionId());

            Map<String, Object> taskServiceVariables = taskService.getVariables(taskId);
            Map<String, Object> runtimeServiceVariables = runtimeService.getVariables(processInstanceId);
            Map<String, Object> variables = runtimeService.getVariables(task.getExecutionId());
            log.debug("任务变量:{}", JSONUtil.toJsonStr(taskServiceVariables));
            log.debug("流程节点:{}", JSONUtil.toJsonStr(runtimeServiceVariables));
            log.debug("执行节点变量:{}", JSONUtil.toJsonStr(variables));

            String taskDefinitionKey = task.getTaskDefinitionKey();
            String processDefinitionId = task.getProcessDefinitionId();
            String flowId = NodeUtil.getFlowId(processDefinitionId);

            TaskDto taskDto = new TaskDto();
            taskDto.setFlowId(flowId);
            taskDto.setTaskCreateTime(task.getCreateTime());
            taskDto.setNodeId(taskDefinitionKey);
            taskDto.setParamMap(taskServiceVariables);
            taskDto.setProcessInstanceId(processInstanceId);
            taskDto.setTaskId(taskId);
            taskDto.setAssign(task.getAssignee());
            taskDto.setTaskName(task.getName());

            taskDtoList.add(taskDto);
        });

        long count = taskQuery.count();

        log.debug("当前有" + count + " 个任务:");

        pageResultDto.setTotal(count);
        pageResultDto.setRecords(taskDtoList);

        return R.ok(pageResultDto);
    }

    /**
     * 查询已完成任务
     * @param taskQueryParamDto 任务查询参数
     * @return 已完成任务列表
     */
    public R<Page<TaskDto>> queryCompletedTask(TaskQueryParamDto taskQueryParamDto) {
        HistoricActivityInstanceQuery historicActivityInstanceQuery = historyService
                .createHistoricActivityInstanceQuery();
        HistoricActivityInstanceQuery activityInstanceQuery = historicActivityInstanceQuery
                .taskAssignee(taskQueryParamDto.getAssign()).finished().orderByHistoricActivityInstanceEndTime().desc();

        if (ArrayUtil.isNotEmpty(taskQueryParamDto.getTaskTime())) {
            ZoneId zoneId = ZoneId.systemDefault();
            ZonedDateTime zonedBeforeDateTime = taskQueryParamDto.getTaskTime()[0].atZone(zoneId);
            ZonedDateTime zonedAfterDateTime = taskQueryParamDto.getTaskTime()[1].atZone(zoneId);
            Date beforeDate = Date.from(zonedBeforeDateTime.toInstant());
            Date afterDate = Date.from(zonedAfterDateTime.toInstant());
            activityInstanceQuery.finishedBefore(afterDate).finishedAfter(beforeDate);
        }

        List<HistoricActivityInstance> list = activityInstanceQuery.listPage(
                (taskQueryParamDto.getPageNum() - 1) * taskQueryParamDto.getPageSize(),
                taskQueryParamDto.getPageSize());

        long count = activityInstanceQuery.count();
        List<TaskDto> taskDtoList = new ArrayList<>();

        for (HistoricActivityInstance historicActivityInstance : list) {
            String activityId = historicActivityInstance.getActivityId();
            String activityName = historicActivityInstance.getActivityName();
            String executionId = historicActivityInstance.getExecutionId();
            String taskId = historicActivityInstance.getTaskId();
            Date startTime = historicActivityInstance.getStartTime();
            Date endTime = historicActivityInstance.getEndTime();
            Long durationInMillis = historicActivityInstance.getDurationInMillis();
            String processInstanceId = historicActivityInstance.getProcessInstanceId();

            String processDefinitionId = historicActivityInstance.getProcessDefinitionId();
            // 流程id
            String flowId = NodeUtil.getFlowId(processDefinitionId);

            TaskDto taskDto = new TaskDto();
            taskDto.setFlowId(flowId);
            taskDto.setTaskCreateTime(startTime);
            taskDto.setTaskEndTime(endTime);
            taskDto.setNodeId(activityId);
            taskDto.setExecutionId(executionId);
            taskDto.setProcessInstanceId(processInstanceId);
            taskDto.setDurationInMillis(durationInMillis);
            taskDto.setTaskId(taskId);
            taskDto.setAssign(historicActivityInstance.getAssignee());
            taskDto.setTaskName(activityName);

            taskDtoList.add(taskDto);
        }

        Page<TaskDto> pageResultDto = new Page<>();
        pageResultDto.setTotal(count);
        pageResultDto.setRecords(taskDtoList);
        return R.ok(pageResultDto);
    }

    /**
     * 完成任务
     * @param taskParamDto 任务参数
     * @return 完成任务结果
     */
    public R<String> completeTask(TaskParamDto taskParamDto) {
        Map<String, Object> taskLocalParamMap = taskParamDto.getTaskLocalParamMap();
        if (CollUtil.isNotEmpty(taskLocalParamMap)) {
            taskService.setVariablesLocal(taskParamDto.getTaskId(), taskLocalParamMap);
        }

        taskService.complete(taskParamDto.getTaskId(), taskParamDto.getParamMap());
        return R.ok();
    }

    /**
     * 设置任务负责人
     * @param taskParamDto 任务参数
     * @return 设置负责人结果
     */
    public R<String> setAssignee(TaskParamDto taskParamDto) {
        return iTaskService.setAssignee(taskParamDto);
    }

    /**
     * 停止流程实例
     * @param taskParamDto 任务参数
     * @return 停止流程实例结果
     */
    public R<String> stopProcessInstance(TaskParamDto taskParamDto) {
        List<String> processInstanceIdList = taskParamDto.getProcessInstanceIdList();
        processInstanceIdList.forEach(processInstanceId -> {
            // 查询流程实例
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId).singleResult();
            if (Optional.ofNullable(processInstance).isPresent()) {
                // 查询执行实例
                List<String> executionIds = runtimeService.createExecutionQuery().parentId(processInstanceId).list()
                        .stream().map(Execution::getId).collect(Collectors.toList());
                // 更改活动状态为结束
                runtimeService.createChangeActivityStateBuilder().moveExecutionsToSingleActivityId(executionIds, "end")
                        .changeState();
            }
        });
        return R.ok();
    }

    /**
     * 解决任务
     * @param taskParamDto 任务参数
     * @return 解决任务结果
     */
    public R<String> resolveTask(TaskParamDto taskParamDto) {
        return iTaskService.resolveTask(taskParamDto);
    }

    /**
     * 退回任务
     * @param taskParamDto 任务参数
     * @return 退回任务结果
     */
    public R<String> back(TaskParamDto taskParamDto) {
        return iTaskService.back(taskParamDto);
    }

    /**
     * 委派任务
     * @param taskParamDto 任务参数
     * @return 委派任务结果
     */
    public R<String> delegateTask(TaskParamDto taskParamDto) {
        return iTaskService.delegateTask(taskParamDto);
    }

    /**
     * 查询任务
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 任务查询结果
     */
    public R<TaskResultDto> queryTask(String taskId, Long userId) {
        Optional<Task> task = Optional
                .ofNullable(taskService.createTaskQuery().taskId(taskId).taskAssignee(userId.toString()).singleResult());

        if (task.isPresent()) {
            String processDefinitionId = task.get().getProcessDefinitionId();
            String taskDefinitionKey = task.get().getTaskDefinitionKey();
            DelegationState delegationState = task.get().getDelegationState();
            String processInstanceId = task.get().getProcessInstanceId();
            Object delegateVariable = taskService.getVariableLocal(taskId, "delegate");

            String flowId = NodeUtil.getFlowId(processDefinitionId);
            Map<String, Object> variables = taskService.getVariables(taskId);
            Map<String, Object> variableAll = new HashMap<>(variables);

            TaskResultDto taskResultDto = new TaskResultDto();
            taskResultDto.setFlowId(flowId);
            taskResultDto.setProcessInstanceId(processDefinitionId);
            taskResultDto.setNodeId(taskDefinitionKey);
            taskResultDto.setCurrentTask(true);
            taskResultDto.setDelegate(Convert.toBool(delegateVariable, false));
            taskResultDto.setVariableAll(variableAll);
            taskResultDto.setProcessInstanceId(processInstanceId);
            taskResultDto.setDelegationState(delegationState == null ? null : delegationState.toString());

            return R.ok(taskResultDto);
        }
        else {
            Optional<HistoricTaskInstance> historicTaskInstance = Optional.ofNullable(historyService
                    .createHistoricTaskInstanceQuery().taskId(taskId).taskAssignee(userId.toString()).singleResult());

            if (historicTaskInstance.isPresent()) {
                String processDefinitionId = historicTaskInstance.get().getProcessDefinitionId();
                String taskDefinitionKey = historicTaskInstance.get().getTaskDefinitionKey();
                String processInstanceId = historicTaskInstance.get().getProcessInstanceId();

                String flowId = NodeUtil.getFlowId(processDefinitionId);
                Map<String, Object> variableAll = new HashMap<>();

                TaskResultDto taskResultDto = new TaskResultDto();
                taskResultDto.setFlowId(flowId);
                taskResultDto.setNodeId(taskDefinitionKey);
                taskResultDto.setCurrentTask(false);
                taskResultDto.setVariableAll(variableAll);
                taskResultDto.setProcessInstanceId(processInstanceId);

                return R.ok(taskResultDto);
            }
            else {
                return R.failed("任务不存在");
            }
        }
    }

}
