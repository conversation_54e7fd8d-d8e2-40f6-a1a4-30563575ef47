# 使用说明 V5.2
# 1. 使用docker-compose  宿主机不需要配置host来发现
# 2. 无需修改源码，根目录  docker-compose up 即可
# 3. 静静等待服务启动

version: '3'
services:
  pigx-mysql:
    build:
      context: ./db
    environment:
      MYSQL_ROOT_HOST: "%"
      MYSQL_ROOT_PASSWORD: root
    restart: always
    container_name: pigx-mysql
    image: pigx-mysql
    volumes:
      - ./pigx-mysql:/var/lib/mysql
    command: --lower_case_table_names=1
    networks:
      - spring_cloud_default

  pigx-redis:
    container_name: pigx-redis
    image: redis:6.2.6
    restart: always
    networks:
      - spring_cloud_default

  pigx-register:
    build:
      context: ./pigx-register
    restart: always
    container_name: pigx-register
    image: pigx-register
    ports:
      - 8848:8848
      - 9848:9848
    networks:
      - spring_cloud_default

  pigx-gateway:
    build:
      context: ./pigx-gateway
    restart: always
    container_name: pigx-gateway
    image: pigx-gateway
    ports:
      - 9999:9999
    networks:
      - spring_cloud_default

  pigx-auth:
    build:
      context: ./pigx-auth
    restart: always
    container_name: pigx-auth
    image: pigx-auth
    networks:
      - spring_cloud_default

  pigx-upms:
    build:
      context: ./pigx-upms/pigx-upms-biz
    restart: always
    container_name: pigx-upms
    image: pigx-upms
    networks:
      - spring_cloud_default

  pigx-flow-task:
    build:
      context: ./pigx-flow/pigx-flow-task/pigx-flow-task-biz
    restart: always
    container_name: pigx-flow-task
    image: pigx-flow-task
    networks:
      - spring_cloud_default

  pigx-flow-engine:
    build:
      context: ./pigx-flow/pigx-flow-engine/pigx-flow-engine-biz
    restart: always
    container_name: pigx-flow-engine
    image: pigx-flow-engine
    networks:
      - spring_cloud_default

  pigx-app-server:
    build:
      context: ./pigx-app-server/pigx-app-server-biz
    restart: always
    container_name: pigx-app-server
    image: pigx-app-server
    networks:
      - spring_cloud_default

  pigx-monitor:
    build:
      context: ./pigx-visual/pigx-monitor
    restart: always
    image: pigx-monitor
    container_name: pigx-monitor
    ports:
      - 5001:5001
    networks:
      - spring_cloud_default

  pigx-daemon-quartz:
    build:
      context: ./pigx-visual/pigx-daemon-quartz
    restart: always
    image: pigx-daemon-quartz
    container_name: pigx-daemon-quartz
    networks:
      - spring_cloud_default

  pigx-daemon-elastic-job:
    build:
      context: ./pigx-visual/pigx-daemon-elastic-job
    restart: always
    image: pigx-daemon-elastic-job
    container_name: pigx-daemon-elastic-job
    networks:
      - spring_cloud_default

  pigx-codegen:
    build:
      context: ./pigx-visual/pigx-codegen
    restart: always
    image: pigx-codegen
    container_name: pigx-codegen
    networks:
      - spring_cloud_default

  pigx-mp-platform:
    build:
      context: ./pigx-visual/pigx-mp-platform
    restart: always
    image: pigx-mp-platform
    container_name: pigx-mp-platform
    networks:
      - spring_cloud_default

  pigx-pay-platform:
    build:
      context: ./pigx-visual/pigx-pay-platform
    restart: always
    image: pigx-pay-platform
    container_name: pigx-pay-platform
    networks:
      - spring_cloud_default

  pigx-report-platform:
    build:
      context: ./pigx-visual/pigx-report-platform
    restart: always
    image: pigx-report-platform
    container_name: pigx-report-platform
    ports:
      - 9095:9095
    networks:
      - spring_cloud_default

  pigx-jimu-platform:
    build:
      context: ./pigx-visual/pigx-jimu-platform
    restart: always
    image: pigx-jimu-platform
    container_name: pigx-jimu-platform
    ports:
      - 5008:5008
    networks:
      - spring_cloud_default

  pigxx-job:
    build:
      context: ./pigx-visual/pigx-xxl-job-admin
    restart: always
    container_name: pigx-job
    hostname: pigx-job
    image: pigx-job
    ports:
      - 9080:9080
    networks:
      - spring_cloud_default

  pigx-sentinel:
    build:
      context: ./pigx-visual/pigx-sentinel-dashboard
    restart: always
    image: pigx-sentinel
    container_name: pigx-sentinel
    ports:
      - 5020:5020
    networks:
      - spring_cloud_default

networks:
  spring_cloud_default:
    name:  spring_cloud_default
    driver: bridge
