INSERT INTO sys_public_param VALUES (10, '老平台的登录地址', 'OLD_PLAT_LOGIN_URL', 'https://training-admin-test.whxunw.com/htgl/login', '0', NULL, ' ', ' ', sysdate, NULL, '2', '0', '0', 1);


--   以下增加业务字段    --
ALTER TABLE SYS_TENANT ADD PORTAL_DOMAIN VARCHAR2(255) NULL;
ALTER TABLE SYS_TENANT ADD PORTAL_NAME VARCHAR2(255) NULL;
ALTER TABLE SYS_TENANT ADD PORTAL_LOGO VARCHAR2(255) NULL;

COMMENT ON COLUMN SYS_TENANT.PORTAL_DOMAIN IS
'门户域名';
COMMENT ON COLUMN SYS_TENANT.PORTAL_DOMAIN IS
'门户网站名称';
COMMENT ON COLUMN SYS_TENANT.PORTAL_DOMAIN IS
'门户LOGO';

ALTER TABLE SYS_TENANT ADD SMS_SIGN VARCHAR2(255) NULL;
COMMENT ON COLUMN SYS_TENANT.SMS_SIGN IS '短信签名';

ALTER TABLE SYS_TENANT ADD IS_PORTAL_CUSTOMIZED VARCHAR2(1) NULL;
COMMENT ON COLUMN SYS_TENANT.IS_PORTAL_CUSTOMIZED IS '是否定制化门户 1是0否';

ALTER TABLE SYS_TENANT ADD IS_CRP_INTEGRATE VARCHAR2(255) NULL;
COMMENT ON COLUMN SYS_TENANT.IS_CRP_INTEGRATE IS '是否已集成CRP 1是0否';

ALTER TABLE SYS_TENANT ADD APPLET_QR_CODE VARCHAR2(255) NULL;
COMMENT ON COLUMN SYS_TENANT.APPLET_QR_CODE IS '微信小程序二维码';

ALTER TABLE SYS_DEPT ADD TENANT_ID NUMBER(20) NULL;
COMMENT ON COLUMN SYS_DEPT.TENANT_ID IS '租户id';


ALTER TABLE SYS_TENANT ADD OLD_ID VARCHAR2(64) NULL;
COMMENT ON COLUMN SYS_TENANT.OLD_ID IS '原平台id';

ALTER TABLE SYS_ORG ADD NEW_ID INTEGER NULL;

UPDATE
    SYS_ORG
SET
    new_id = 10000 + (
        SELECT
            rn
        FROM
            (
                SELECT
                    rowid AS rid,
                    ROW_NUMBER() OVER (
			ORDER BY id) AS rn
                FROM
                    SYS_ORG
            ) tmp
        WHERE
            tmp.rid = SYS_ORG.rowid
    );

--  生成租户 insert sql
INSERT INTO SYS_TENANT
(ID, NAME, CODE, START_TIME, END_TIME, STATUS, DEL_FLAG, CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME,PORTAL_DOMAIN,PORTAL_NAME,PORTAL_LOGO,SMS_SIGN,IS_PORTAL_CUSTOMIZED,IS_CRP_INTEGRATE,APPLET_QR_CODE, OLD_ID)
SELECT
    so.NEW_ID id ,so.name name, so.code code, sysdate start_time, sysdate end_time, 0 status, 0 del_flag, 'admin' create_by, 'admin' update_by,
    sysdate create_time, sysdate update_time, so.PORTAL_DOMAIN , so.PORTAL_SYS_NAME , so.PORTAL_LOGO ,so.SMS_SIGN , so.IS_PORTAL_CUSTOMIZED , so.IS_CRP_INTEGRATE , so.APPLET_QR_CODE, so.ID OLD_ID
FROM SYS_ORG so
WHERE so.IS_PARENT = 1 AND so.ORG_TYPE = 'HOST_ORG';

--  生成部门 insert sql
INSERT INTO SYS_DEPT
(DEPT_ID, NAME, SORT_ORDER, CREATE_BY, UPDATE_BY, PARENT_ID, TENANT_ID)
SELECT so.NEW_ID dept_id ,so.name name, 1 sort_order, 'admin' create_by, 'admin' update_by, po.NEW_ID PARENT_ID, po.NEW_ID tenant_id FROM SYS_ORG so
    LEFT JOIN SYS_ORG po ON po.id = so.parent_id
WHERE so.IS_PARENT IS NULL OR so.is_parent = 0;

--         用户
ALTER TABLE SYS_USER_OLD ADD NEW_ID INTEGER NULL;

ALTER TABLE SYS_USER ADD OLD_ID VARCHAR2(64) NULL;
COMMENT ON COLUMN SYS_USER.OLD_ID IS '原平台id';

UPDATE
    SYS_USER_OLD
SET
    new_id = 10000 + (
        SELECT
            rn
        FROM
            (
                SELECT
                    rowid AS rid,
                    ROW_NUMBER() OVER (
			ORDER BY new_id) AS rn
                FROM
                    SYS_USER_OLD
            ) tmp
        WHERE
            tmp.rid = SYS_USER_OLD.rowid
    );


--    一级部门用户，  二级三级 需要重写sql
INSERT INTO SYS_USER(USER_ID,USERNAME,NAME , PHONE ,PASSWORD,CREATE_BY,UPDATE_BY,TENANT_ID,OLD_ID)
SELECT
    su.new_id user_id, USERNAME, su.NAME, su.MOBILE, PASSWORD,  'admin' create_by, 'admin' update_by, o.NEW_ID tenant_id, su.id
FROM SYS_USER_OLD su
         LEFT JOIN SYS_ORG o ON o.id = su.ORG_ID
WHERE o.IS_PARENT = 1 and su.username <> 'admin';

--  二级 用户
    INSERT INTO SYS_USER(USER_ID,USERNAME,NAME , PHONE, PASSWORD,DEPT_ID,CREATE_BY,UPDATE_BY,TENANT_ID,OLD_ID)
SELECT
    su.new_id user_id, USERNAME, su.NAME, su.MOBILE, PASSWORD, o.new_id detp_id,  'admin' create_by, 'admin' update_by, op.NEW_ID tenant_id, su.id
FROM SYS_USER_OLD su
         LEFT JOIN SYS_ORG o ON o.id = su.ORG_ID
         LEFT JOIN SYS_ORG op ON op.id = o.parent_id
WHERE op.IS_PARENT = 1;

-- 用户角色
INSERT INTO SYS_USER_ROLE(USER_ID, ROLE_ID)
SELECT distinct suo.NEW_ID, sr.ROLE_ID FROM sys_user_2_role r
inner JOIN SYS_USER_OLD suo ON suo.ID = r.USER_ID
INNER JOIN SYS_ROLE sr ON sr.ROLE_CODE = r."ROLE"










