



create table process
(
    id                 INTEGER              not null,
    del_flag           SMALLINT             not null,
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    flow_id            VARCHAR2(255)        default '',
    name               VARCHAR2(50)         not null,
    logo               VARCHAR2(200)        not null,
    settings           NCLOB                 default NULL,
    group_id           INTEGER              not null,
    form_items         NCLOB                 not null,
    process            NCLOB                 not null,
    remark             VARCHAR2(125)        default NULL,
    sort               INTEGER              not null,
    is_hidden          SMALLINT             not null,
    is_stop            SMALLINT             not null,
    admin_id           INTEGER              default NULL,
    unique_id          VARCHAR2(50)         default NULL,
    admin_list         VARCHAR2(255)        default NULL,
    range_show         VARCHAR2(255)        default NULL,
    tenant_id          INTEGER              default 0 not null,
    constraint PK_PROCESS primary key (id),
    constraint idx_form_id unique (flow_id)
);

comment on column process.id is
'用户id';

comment on column process.del_flag is
'逻辑删除字段';

comment on column process.create_time is
'创建时间';

comment on column process.update_time is
'更新时间';

comment on column process.flow_id is
'表单ID';

comment on column process.name is
'表单名称';

comment on column process.logo is
'图标配置';

comment on column process.settings is
'设置项';

comment on column process.group_id is
'分组ID';

comment on column process.form_items is
'表单设置内容';

comment on column process.process is
'流程设置内容';

comment on column process.remark is
'备注';

comment on column process.is_hidden is
'0 正常 1=隐藏';

comment on column process.is_stop is
'0 正常 1=停用 ';

comment on column process.admin_id is
'流程管理员';

comment on column process.unique_id is
'唯一性id';

comment on column process.admin_list is
'管理员';

comment on column process.range_show is
'范围描述显示';

comment on column process.tenant_id is
'所属租户id';

/*==============================================================*/
/* Table: process_copy                                        */
/*==============================================================*/
create table process_copy
(
    id                 INTEGER              not null,
    del_flag           SMALLINT             not null,
    create_time        DATE                 not null,
    update_time        DATE                 not null,
    start_time         DATE                 not null,
    node_time          DATE                 not null,
    start_user_id      INTEGER              not null,
    flow_id            VARCHAR2(255)        not null,
    process_instance_id VARCHAR2(255)        not null,
    node_id            VARCHAR2(255)        not null,
    group_id           INTEGER              not null,
    group_name         VARCHAR2(255)        not null,
    process_name       VARCHAR2(255)        not null,
    node_name          VARCHAR2(255)        not null,
    form_data          CLOB                 not null,
    user_id            INTEGER              not null,
    tenant_id          INTEGER              default 0 not null,
    constraint PK_PROCESS_COPY primary key (id)
);





comment on table process_copy is
'流程抄送数据';

comment on column process_copy.id is
'用户id';

comment on column process_copy.del_flag is
'逻辑删除字段';

comment on column process_copy.create_time is
'创建时间';

comment on column process_copy.update_time is
'更新时间';

comment on column process_copy.start_time is
' 流程发起时间';

comment on column process_copy.node_time is
'当前节点时间';

comment on column process_copy.start_user_id is
'发起人';

comment on column process_copy.flow_id is
'流程id';

comment on column process_copy.process_instance_id is
'实例id';

comment on column process_copy.node_id is
'节点id';

comment on column process_copy.group_id is
'分组id';

comment on column process_copy.group_name is
'分组名称';

comment on column process_copy.process_name is
'流程名称';

comment on column process_copy.node_name is
'节点 名称';

comment on column process_copy.form_data is
'表单数据';

comment on column process_copy.user_id is
'抄送人id';

comment on column process_copy.tenant_id is
'所属租户id';

/*==============================================================*/
/* Table: process_group                                       */
/*==============================================================*/
create table process_group
(
    id                 INTEGER              not null,
    del_flag           SMALLINT             not null,
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    group_name         VARCHAR2(50)         not null,
    sort               INTEGER              default 0 not null,
    tenant_id          INTEGER              default 0 not null,
    constraint PK_PROCESS_GROUP primary key (id)
);

comment on column process_group.id is
'用户id';

comment on column process_group.del_flag is
'逻辑删除字段';

comment on column process_group.create_time is
'创建时间';

comment on column process_group.update_time is
'更新时间';

comment on column process_group.group_name is
'分组名';

comment on column process_group.sort is
'排序';

comment on column process_group.tenant_id is
'所属租户id';

/*==============================================================*/
/* Table: process_instance_record                             */
/*==============================================================*/
create table process_instance_record
(
    id                 INTEGER              not null,
    name               VARCHAR2(50)         not null,
    logo               VARCHAR2(200)        not null,
    user_id            INTEGER              not null,
    del_flag           SMALLINT             not null,
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    flow_id            VARCHAR2(255)        default NULL,
    process_instance_id VARCHAR2(50)         default NULL,
    form_data          CLOB,
    group_id           INTEGER              default NULL,
    group_name         VARCHAR2(100)        default NULL,
    status             INTEGER              default 1,
    end_time           DATE                 default NULL,
    parent_process_instance_id VARCHAR2(50)         default NULL,
    tenant_id          INTEGER              default 0 not null,
    constraint PK_PROCESS_INSTANCE_RECORD primary key (id)
);

CREATE INDEX idx_dep_id ON process_instance_record(user_id);


comment on column process_instance_record.id is
'用户id';

comment on column process_instance_record.name is
'流程名字';

comment on column process_instance_record.logo is
'头像';

comment on column process_instance_record.user_id is
'用户id';

comment on column process_instance_record.del_flag is
'逻辑删除字段';

comment on column process_instance_record.create_time is
'创建时间';

comment on column process_instance_record.flow_id is
'流程id';

comment on column process_instance_record.process_instance_id is
'流程实例id';

comment on column process_instance_record.form_data is
'表单数据';

comment on column process_instance_record.group_id is
'组id';

comment on column process_instance_record.group_name is
'组名称';

comment on column process_instance_record.status is
'状态';

comment on column process_instance_record.end_time is
'结束时间';

comment on column process_instance_record.parent_process_instance_id is
'上级流程实例id';

comment on column process_instance_record.tenant_id is
'所属租户id';

/*==============================================================*/
/* Table: process_node_assign_user                            */
/*==============================================================*/
create table process_node_assign_user
(
    id                 INTEGER              not null,
    del_flag           SMALLINT             not null,
    create_time        DATE                 not null,
    update_time        DATE                 not null,
    flow_id            VARCHAR2(255)        not null,
    process_instance_id VARCHAR2(50)         not null,
    data               CLOB,
    node_id            VARCHAR2(50)         not null,
    user_id            VARCHAR2(50)         not null,
    status             INTEGER              not null,
    start_time         DATE                 not null,
    end_time           DATE                 default NULL,
    execution_id       VARCHAR2(255)        default NULL,
    task_id            VARCHAR2(255)        default NULL,
    approve_desc       VARCHAR2(1000)       default NULL,
    node_name          VARCHAR2(255)        default NULL,
    task_type          VARCHAR2(255)        default NULL,
    local_data         CLOB,
    tenant_id          INTEGER              default 0 not null,
    constraint PK_PROCESS_NODE_ASSIGN_USER primary key (id)
);

comment on column process_node_assign_user.id is
'用户id';

comment on column process_node_assign_user.del_flag is
'逻辑删除字段';

comment on column process_node_assign_user.create_time is
'创建时间';

comment on column process_node_assign_user.update_time is
'更新时间';

comment on column process_node_assign_user.flow_id is
'流程id';

comment on column process_node_assign_user.process_instance_id is
'流程实例id';

comment on column process_node_assign_user.data is
'表单数据';

comment on column process_node_assign_user.user_id is
' 用户id';

comment on column process_node_assign_user.status is
'节点状态';

comment on column process_node_assign_user.start_time is
'开始时间';

comment on column process_node_assign_user.end_time is
'结束时间';

comment on column process_node_assign_user.execution_id is
'执行id';

comment on column process_node_assign_user.task_id is
' 任务id';

comment on column process_node_assign_user.approve_desc is
'审批意见';

comment on column process_node_assign_user.node_name is
' 节点名称';

comment on column process_node_assign_user.task_type is
'任务类型';

comment on column process_node_assign_user.local_data is
'表单本地数据';

comment on column process_node_assign_user.tenant_id is
'所属租户id';

/*==============================================================*/
/* Table: process_node_data                                   */
/*==============================================================*/
create table process_node_data
(
    id                 INTEGER              not null,
    del_flag           SMALLINT             not null,
    create_time        DATE                 not null,
    update_time        DATE                 not null,
    flow_id            VARCHAR2(255)        not null,
    data               CLOB                 not null,
    node_id            VARCHAR2(50)         not null,
    tenant_id          INTEGER              default 0 not null,
    constraint PK_PROCESS_NODE_DATA primary key (id)
);

comment on column process_node_data.id is
'用户id';

comment on column process_node_data.del_flag is
'逻辑删除字段';

comment on column process_node_data.create_time is
'创建时间';

comment on column process_node_data.update_time is
'更新时间';

comment on column process_node_data.flow_id is
'流程id';

comment on column process_node_data.data is
'表单数据';

comment on column process_node_data.tenant_id is
'所属租户id';

/*==============================================================*/
/* Table: process_node_record                                 */
/*==============================================================*/
create table process_node_record
(
    id                 INTEGER              not null,
    del_flag           SMALLINT             not null,
    create_time        DATE                 not null,
    update_time        DATE                 not null,
    flow_id            VARCHAR2(255)        not null,
    process_instance_id VARCHAR2(50)         not null,
    data               CLOB,
    node_id            VARCHAR2(50)         not null,
    node_type          VARCHAR2(50)         default NULL,
    node_name          VARCHAR2(50)         not null,
    status             INTEGER              not null,
    start_time         DATE                 not null,
    end_time           DATE                 default NULL,
    execution_id       VARCHAR2(255)        default NULL,
    tenant_id          INTEGER              default 0 not null,
    constraint PK_PROCESS_NODE_RECORD primary key (id)
);

comment on column process_node_record.id is
'用户id';

comment on column process_node_record.del_flag is
'逻辑删除字段';

comment on column process_node_record.create_time is
'创建时间';

comment on column process_node_record.update_time is
'更新时间';

comment on column process_node_record.flow_id is
'流程id';

comment on column process_node_record.process_instance_id is
'流程实例id';

comment on column process_node_record.data is
'表单数据';

comment on column process_node_record.node_type is
'节点类型';

comment on column process_node_record.node_name is
'节点名字';

comment on column process_node_record.status is
'节点状态';

comment on column process_node_record.start_time is
'开始时间';

comment on column process_node_record.end_time is
'结束时间';

comment on column process_node_record.execution_id is
'执行id';

comment on column process_node_record.tenant_id is
'所属租户id';

/*==============================================================*/
/* Table: process_starter                                     */
/*==============================================================*/
create table process_starter
(
    id                 INTEGER              not null,
    del_flag           SMALLINT             not null,
    create_time        DATE                 not null,
    update_time        DATE                 not null,
    type_id            INTEGER              not null,
    type               VARCHAR2(50)         not null,
    process_id         INTEGER              not null,
    tenant_id          INTEGER              default 0 not null,
    constraint PK_PROCESS_STARTER primary key (id)
);

comment on table process_starter is
'流程发起人';

comment on column process_starter.id is
'用户id';

comment on column process_starter.del_flag is
'逻辑删除字段';

comment on column process_starter.create_time is
'创建时间';

comment on column process_starter.update_time is
'更新时间';

comment on column process_starter.type_id is
'用户id或者部门id';

comment on column process_starter.type is
' 类型 user dept';

comment on column process_starter.process_id is
'流程id';

comment on column process_starter.tenant_id is
'所属租户id';

/*==============================================================*/
/* Table: sys_audit_log                                       */
/*==============================================================*/
create table sys_audit_log
(
    id                 INTEGER              not null,
    audit_name         VARCHAR2(255)        not null,
    audit_field        VARCHAR2(255)        not null,
    before_val         VARCHAR2(255)        default NULL,
    after_val          VARCHAR2(255)        default NULL,
    create_by          VARCHAR2(64)         default NULL,
    create_time        DATE                 not null,
    del_flag           CHAR(1)              not null,
    tenant_id          INTEGER              not null,
    constraint PK_SYS_AUDIT_LOG primary key (id)
);

comment on table sys_audit_log is
'审计记录表';

comment on column sys_audit_log.id is
'主键';

comment on column sys_audit_log.audit_name is
'审计名称';

comment on column sys_audit_log.audit_field is
'字段名称';

comment on column sys_audit_log.before_val is
'变更前值';

comment on column sys_audit_log.after_val is
'变更后值';

comment on column sys_audit_log.create_by is
'操作人';

comment on column sys_audit_log.create_time is
'操作时间';

comment on column sys_audit_log.del_flag is
'删除标记';

comment on column sys_audit_log.tenant_id is
'租户ID';

/*==============================================================*/
/* Table: sys_dept                                            */
/*==============================================================*/
create table sys_dept
(
    dept_id            INTEGER              not null,
    name               VARCHAR2(50)         default NULL,
    sort_order         INTEGER              default 0 not null,
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    del_flag           CHAR(1)              default '0',
    parent_id          INTEGER              default NULL,
    tenant_id          INTEGER              default NULL,
    constraint PK_SYS_DEPT primary key (dept_id)
);

comment on table sys_dept is
'部门管理';

comment on column sys_dept.dept_id is
'部门ID';

comment on column sys_dept.name is
'部门名称';

comment on column sys_dept.sort_order is
'排序';

comment on column sys_dept.create_by is
'创建人';

comment on column sys_dept.update_by is
'修改人';

comment on column sys_dept.create_time is
'创建时间';

comment on column sys_dept.update_time is
'修改时间';

comment on column sys_dept.del_flag is
'删除标志';

comment on column sys_dept.parent_id is
'父级部门ID';

comment on column sys_dept.tenant_id is
'租户ID';

/*==============================================================*/
/* Table: sys_dict                                            */
/*==============================================================*/
create table sys_dict
(
    id                 INTEGER              not null,
    dict_type          VARCHAR2(100)        default NULL,
    description        VARCHAR2(100)        default NULL,
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    remarks            VARCHAR2(255)        default NULL,
    system_flag        CHAR(1)              default '0',
    del_flag           CHAR(1)              default '0',
    tenant_id          INTEGER              default 0 not null,
    constraint PK_SYS_DICT primary key (id)
);
CREATE INDEX sys_dict_del_flag ON sys_dict(del_flag);

comment on table sys_dict is
'字典表';

comment on column sys_dict.id is
'编号';

comment on column sys_dict.dict_type is
'字典类型';

comment on column sys_dict.description is
'描述';

comment on column sys_dict.create_by is
'创建人';

comment on column sys_dict.update_by is
'修改人';

comment on column sys_dict.create_time is
'创建时间';

comment on column sys_dict.update_time is
'更新时间';

comment on column sys_dict.remarks is
'备注信息';

comment on column sys_dict.system_flag is
'系统标志';

comment on column sys_dict.del_flag is
'删除标志';

comment on column sys_dict.tenant_id is
'所属租户';

/*==============================================================*/
/* Table: sys_dict_item                                       */
/*==============================================================*/
create table sys_dict_item
(
    id                 INTEGER              not null,
    dict_id            INTEGER              not null,
    item_value         VARCHAR2(100)        default NULL,
    label              VARCHAR2(100)        default NULL,
    dict_type          VARCHAR2(100)        default NULL,
    description        VARCHAR2(100)        default NULL,
    sort_order         INTEGER              default 0 not null,
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    remarks            VARCHAR2(255)        default NULL,
    del_flag           CHAR(1)              default '0',
    tenant_id          INTEGER              default 0 not null,
    constraint PK_SYS_DICT_ITEM primary key (id)
);

CREATE INDEX sys_dict_value ON sys_dict_item(item_value);
CREATE INDEX sys_dict_label ON sys_dict_item(label);
CREATE INDEX sys_dict_item_del_flag ON sys_dict_item(del_flag);

comment on table sys_dict_item is
'字典项';

comment on column sys_dict_item.id is
'编号';

comment on column sys_dict_item.dict_id is
'字典ID';

comment on column sys_dict_item.item_value is
'字典项值';

comment on column sys_dict_item.label is
'字典项名称';

comment on column sys_dict_item.dict_type is
'字典类型';

comment on column sys_dict_item.description is
'字典项描述';

comment on column sys_dict_item.sort_order is
'排序（升序）';

comment on column sys_dict_item.create_by is
'创建人';

comment on column sys_dict_item.update_by is
'修改人';

comment on column sys_dict_item.create_time is
'创建时间';

comment on column sys_dict_item.update_time is
'更新时间';

comment on column sys_dict_item.remarks is
'备注信息';

comment on column sys_dict_item.del_flag is
'删除标志';

comment on column sys_dict_item.tenant_id is
'所属租户';

/*==============================================================*/
/* Table: sys_file                                            */
/*==============================================================*/
create table sys_file
(
    id                 INTEGER              not null,
    group_id           INTEGER              default NULL,
    file_name          VARCHAR2(100)        default NULL,
    bucket_name        VARCHAR2(200)        default NULL,
    dir                VARCHAR2(200)        default NULL,
    original           VARCHAR2(100)        default NULL,
    type               VARCHAR2(50)         default NULL,
    file_size          INTEGER              default NULL,
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    del_flag           CHAR(1)              default '0',
    tenant_id          INTEGER              default NULL,
    constraint PK_SYS_FILE primary key (id)
);

comment on table sys_file is
'文件管理表';

comment on column sys_file.id is
'编号';

comment on column sys_file.group_id is
'文件组';

comment on column sys_file.file_name is
'文件名';

comment on column sys_file.bucket_name is
'文件存储桶名称';

comment on column sys_file.dir is
'文件夹名称';

comment on column sys_file.original is
'原始文件名';

comment on column sys_file.type is
'文件类型';

comment on column sys_file.file_size is
'文件大小';

comment on column sys_file.create_by is
'创建人';

comment on column sys_file.update_by is
'修改人';

comment on column sys_file.create_time is
'上传时间';

comment on column sys_file.update_time is
'更新时间';

comment on column sys_file.del_flag is
'删除标志';

comment on column sys_file.tenant_id is
'所属租户';

/*==============================================================*/
/* Table: sys_file_group                                      */
/*==============================================================*/
create table sys_file_group
(
    id                 INTEGER              not null,
    type               SMALLINT             default 10,
    name               VARCHAR2(32)         default '',
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    del_flag           CHAR(1)              default '0',
    create_by          VARCHAR2(64)         default NULL,
    update_by          VARCHAR2(64)         default NULL,
    tenant_id          INTEGER              default NULL,
    pid                INTEGER              default NULL,
    constraint PK_SYS_FILE_GROUP primary key (id)
);

comment on column sys_file_group.id is
'主键ID';

comment on column sys_file_group.type is
'类型: [10=图片, 20=视频]';

comment on column sys_file_group.name is
'分类名称';

comment on column sys_file_group.create_time is
'创建时间';

comment on column sys_file_group.update_time is
'更新时间';

comment on column sys_file_group.del_flag is
'删除标记';

comment on column sys_file_group.create_by is
'创建人';

comment on column sys_file_group.update_by is
'修改人';

comment on column sys_file_group.tenant_id is
'租户';

comment on column sys_file_group.pid is
'父ID';

/*==============================================================*/
/* Table: sys_i18n                                            */
/*==============================================================*/
create table sys_i18n
(
    id                 INTEGER              not null,
    name               VARCHAR2(255)        not null,
    zh_cn              VARCHAR2(255)        not null,
    en                 VARCHAR2(255)        not null,
    create_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default sysdate,
    update_by          VARCHAR2(64)         default ' ' not null,
    update_time        DATE                 default NULL,
    del_flag           CHAR(1)              default '0',
    constraint PK_SYS_I18N primary key (id)
);




comment on table sys_i18n is
'系统表-国际化';

comment on column sys_i18n.id is
'id';

comment on column sys_i18n.name is
'name';

comment on column sys_i18n.zh_cn is
'中文';

comment on column sys_i18n.en is
'英文';

comment on column sys_i18n.create_by is
'创建人';

comment on column sys_i18n.create_time is
'创建时间';

comment on column sys_i18n.update_by is
'修改人';

comment on column sys_i18n.del_flag is
'删除标记';

/*==============================================================*/
/* Table: sys_log                                             */
/*==============================================================*/
create table sys_log
(
    id                 INTEGER              not null,
    log_type           CHAR(1)              default '0',
    title              VARCHAR2(255)        default NULL,
    service_id         VARCHAR2(32)         default NULL,
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    remote_addr        VARCHAR2(255)        default NULL,
    user_agent         VARCHAR2(1000)       default NULL,
    request_uri        VARCHAR2(255)        default NULL,
    method             VARCHAR2(10)         default NULL,
    params             CLOB,
    time               INTEGER              default NULL,
    del_flag           CHAR(1)              default '0',
    exception          CLOB,
    tenant_id          INTEGER              default 0,
    constraint PK_SYS_LOG primary key (id)
);

CREATE INDEX sys_log_request_uri ON sys_log(request_uri);
CREATE INDEX sys_log_type ON sys_log(log_type);
CREATE INDEX sys_log_create_date ON sys_log(create_time);


comment on table sys_log is
'日志表';

comment on column sys_log.id is
'编号';

comment on column sys_log.log_type is
'日志类型';

comment on column sys_log.title is
'日志标题';

comment on column sys_log.service_id is
'服务ID';

comment on column sys_log.create_by is
'创建人';

comment on column sys_log.update_by is
'修改人';

comment on column sys_log.create_time is
'创建时间';

comment on column sys_log.update_time is
'更新时间';

comment on column sys_log.remote_addr is
'远程地址';

comment on column sys_log.user_agent is
'用户代理';

comment on column sys_log.request_uri is
'请求URI';

comment on column sys_log.method is
'请求方法';

comment on column sys_log.params is
'请求参数';

comment on column sys_log.time is
'执行时间';

comment on column sys_log.del_flag is
'删除标志';

comment on column sys_log.exception is
'异常信息';

comment on column sys_log.tenant_id is
'所属租户';

/*==============================================================*/
/* Table: sys_menu                                            */
/*==============================================================*/

create table sys_menu
(
    menu_id            INTEGER              not null,
    name               VARCHAR2(32)         default NULL,
    permission         VARCHAR2(32)         default NULL,
    path               VARCHAR2(128)        default NULL,
    parent_id          INTEGER              default NULL,
    icon               VARCHAR2(64)         default NULL,
    visible            CHAR(1)              default '1',
    sort_order         INTEGER              default 1,
    keep_alive         CHAR(1)              default '0',
    embedded           CHAR(1)              default NULL,
    menu_type          CHAR(1)              default '0',
    create_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default sysdate,
    update_by          VARCHAR2(64)         default ' ' not null,
    update_time        DATE                 default NULL,
    del_flag           CHAR(1)              default '0',
    tenant_id          INTEGER              default NULL,
    constraint PK_SYS_MENU primary key (menu_id)
);

comment on table sys_menu is
'菜单权限表';

comment on column sys_menu.menu_id is
'菜单ID';

comment on column sys_menu.name is
'菜单名称';

comment on column sys_menu.permission is
'权限标识';

comment on column sys_menu.path is
'路由路径';

comment on column sys_menu.parent_id is
'父菜单ID';

comment on column sys_menu.icon is
'菜单图标';

comment on column sys_menu.visible is
'是否可见，0隐藏，1显示';

comment on column sys_menu.sort_order is
'排序值，越小越靠前';

comment on column sys_menu.keep_alive is
'是否缓存，0否，1是';

comment on column sys_menu.embedded is
'是否内嵌，0否，1是';

comment on column sys_menu.menu_type is
'菜单类型，0目录，1菜单，2按钮';

comment on column sys_menu.create_by is
'创建人';

comment on column sys_menu.create_time is
'创建时间';

comment on column sys_menu.update_by is
'修改人';

comment on column sys_menu.del_flag is
'删除标志，0未删除，1已删除';

comment on column sys_menu.tenant_id is
'租户ID';

/*==============================================================*/
/* Table: sys_message                                         */
/*==============================================================*/
create table sys_message
(
    id                 INTEGER              not null,
    category           VARCHAR2(255),
    title              VARCHAR2(255),
    content            CLOB,
    send_flag          CHAR(1),
    all_flag           CHAR(1)              default '0',
    sort               INTEGER              default 0 not null,
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    create_by          VARCHAR2(32)         default NULL,
    update_by          VARCHAR2(32)         default NULL,
    del_flag           CHAR(1)              not null,
    tenant_id          INTEGER              default NULL,
    constraint PK_SYS_MESSAGE primary key (id)
);

comment on table sys_message is
'站内信息';

comment on column sys_message.id is
'主键';

comment on column sys_message.all_flag is
'全部接受';

comment on column sys_message.sort is
'排序 （越大越在前）';

comment on column sys_message.create_time is
'创建时间';

comment on column sys_message.update_time is
'更新时间';

comment on column sys_message.create_by is
'创建人';

comment on column sys_message.update_by is
'更新人';

comment on column sys_message.del_flag is
'删除时间';

comment on column sys_message.tenant_id is
'租户';

/*==============================================================*/
/* Table: sys_message_relation                                */
/*==============================================================*/
create table sys_message_relation
(
    id                 INTEGER              not null,
    msg_id             INTEGER              default NULL,
    user_id            INTEGER              default NULL,
    content            CLOB,
    read_flag          CHAR(1)              default '0',
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    create_by          VARCHAR2(32)         not null,
    update_by          VARCHAR2(32)         default NULL,
    del_flag           CHAR(1)              not null,
    tenant_id          INTEGER              default NULL,
    constraint PK_SYS_MESSAGE_RELATION primary key (id)
);

comment on table sys_message_relation is
'系统消息推送记录';

comment on column sys_message_relation.id is
'主键';

comment on column sys_message_relation.msg_id is
'消息ID';

comment on column sys_message_relation.user_id is
'接收人ID';

comment on column sys_message_relation.content is
'内容';

comment on column sys_message_relation.read_flag is
'已读（0否，1是）';

comment on column sys_message_relation.create_time is
'创建时间';

comment on column sys_message_relation.update_time is
'更新时间';

comment on column sys_message_relation.create_by is
'创建人';

comment on column sys_message_relation.update_by is
'更新人';

comment on column sys_message_relation.del_flag is
'删除时间';

comment on column sys_message_relation.tenant_id is
'租户';


/*==============================================================*/
/* Table: sys_oauth_client_details                            */
/*==============================================================*/
create table sys_oauth_client_details
(
    id                 INTEGER              not null,
    client_id          VARCHAR2(32)         not null,
    resource_ids       VARCHAR2(256)        default NULL,
    client_secret      VARCHAR2(256)        default NULL,
    scope              VARCHAR2(256)        default NULL,
    authorized_grant_types VARCHAR2(256)        default NULL,
    web_server_redirect_uri VARCHAR2(256)        default NULL,
    authorities        VARCHAR2(256)        default NULL,
    access_token_validity INTEGER              default NULL,
    refresh_token_validity INTEGER              default NULL,
    additional_information NCLOB       default NULL,
    autoapprove        VARCHAR2(256)        default NULL,
    del_flag           CHAR(1)              default '0',
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default NULL,
    update_time        DATE                 default NULL,
    tenant_id          INTEGER              default 0 not null,
    constraint PK_SYS_OAUTH_CLIENT_DETAILS primary key (id)
);

comment on table sys_oauth_client_details is
'终端信息表';

comment on column sys_oauth_client_details.id is
'ID';

comment on column sys_oauth_client_details.client_id is
'客户端ID';

comment on column sys_oauth_client_details.resource_ids is
'资源ID集合';

comment on column sys_oauth_client_details.client_secret is
'客户端秘钥';

comment on column sys_oauth_client_details.scope is
'授权范围';

comment on column sys_oauth_client_details.authorized_grant_types is
'授权类型';

comment on column sys_oauth_client_details.web_server_redirect_uri is
'回调地址';

comment on column sys_oauth_client_details.authorities is
'权限集合';

comment on column sys_oauth_client_details.access_token_validity is
'访问令牌有效期（秒）';

comment on column sys_oauth_client_details.refresh_token_validity is
'刷新令牌有效期（秒）';

comment on column sys_oauth_client_details.additional_information is
'附加信息';

comment on column sys_oauth_client_details.autoapprove is
'自动授权';

comment on column sys_oauth_client_details.del_flag is
'删除标记，0未删除，1已删除';

comment on column sys_oauth_client_details.create_by is
'创建人';

comment on column sys_oauth_client_details.update_by is
'修改人';

comment on column sys_oauth_client_details.create_time is
'创建时间';

comment on column sys_oauth_client_details.update_time is
'更新时间';

comment on column sys_oauth_client_details.tenant_id is
'所属租户ID';

/*==============================================================*/
/* Table: sys_post                                            */
/*==============================================================*/
create table sys_post
(
    post_id            INTEGER              not null,
    post_code          VARCHAR2(64)         not null,
    post_name          VARCHAR2(50)         not null,
    post_sort          INTEGER              not null,
    remark             VARCHAR2(500)        default NULL,
    del_flag           CHAR(1)              default '0' not null,
    create_time        DATE                 default NULL,
    create_by          VARCHAR2(64)         default '' not null,
    update_time        DATE                 default NULL,
    update_by          VARCHAR2(64)         default '' not null,
    tenant_id          INTEGER              default NULL,
    constraint PK_SYS_POST primary key (post_id)
);

comment on table sys_post is
'岗位信息表';

comment on column sys_post.post_id is
'岗位ID';

comment on column sys_post.post_code is
'岗位编码';

comment on column sys_post.post_name is
'岗位名称';

comment on column sys_post.post_sort is
'岗位排序';

comment on column sys_post.remark is
'岗位描述';

comment on column sys_post.del_flag is
'是否删除  -1：已删除  0：正常';

comment on column sys_post.create_time is
'创建时间';

comment on column sys_post.create_by is
'创建人';

comment on column sys_post.update_time is
'更新时间';

comment on column sys_post.update_by is
'更新人';

comment on column sys_post.tenant_id is
'租户ID';

/*==============================================================*/
/* Table: sys_public_param                                    */
/*==============================================================*/

create table sys_public_param
(
    public_id          INTEGER              not null,
    public_name        VARCHAR2(128)        default NULL,
    public_key         VARCHAR2(128)        default NULL,
    public_value       VARCHAR2(128)        default NULL,
    status             CHAR(1)              default '0',
    validate_code      VARCHAR2(64)         default NULL,
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default sysdate not null,
    update_time        DATE                 default NULL,
    public_type        CHAR(1)              default '0',
    system_flag        CHAR(1)              default '0',
    del_flag           CHAR(1)              default '0',
    tenant_id          INTEGER              default NULL,
    constraint PK_SYS_PUBLIC_PARAM primary key (public_id)
);

comment on table sys_public_param is
'公共参数配置表';

comment on column sys_public_param.public_id is
'编号';

comment on column sys_public_param.public_name is
'名称';

comment on column sys_public_param.public_key is
'键';

comment on column sys_public_param.public_value is
'值';

comment on column sys_public_param.status is
'状态，0禁用，1启用';

comment on column sys_public_param.validate_code is
'校验码';

comment on column sys_public_param.create_by is
'创建人';

comment on column sys_public_param.update_by is
'修改人';

comment on column sys_public_param.create_time is
'创建时间';

comment on column sys_public_param.public_type is
'类型，0未知，1系统，2业务';

comment on column sys_public_param.system_flag is
'系统标识，0非系统，1系统';

comment on column sys_public_param.del_flag is
'删除标记，0未删除，1已删除';

comment on column sys_public_param.tenant_id is
'租户ID';

/*==============================================================*/
/* Table: sys_role                                            */
/*==============================================================*/
create table sys_role
(
    role_id            INTEGER              not null,
    role_name          VARCHAR2(64)         default NULL,
    role_code          VARCHAR2(64)         default NULL,
    role_desc          VARCHAR2(255)        default NULL,
    ds_type            CHAR(1)              default '2',
    ds_scope           VARCHAR2(255)        default NULL,
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default sysdate not null,
    update_time        DATE                 default NULL,
    del_flag           CHAR(1)              default '0',
    tenant_id          INTEGER              default NULL,
    constraint PK_SYS_ROLE primary key (role_id)
);
CREATE INDEX role_idx1_role_code ON sys_role(role_code);


comment on table sys_role is
'系统角色表';

comment on column sys_role.role_id is
'角色ID';

comment on column sys_role.role_name is
'角色名称';

comment on column sys_role.role_code is
'角色编码';

comment on column sys_role.role_desc is
'角色描述';

comment on column sys_role.ds_type is
'数据权限类型，0全部，1自定义，2本部门及以下，3本部门，4仅本人';

comment on column sys_role.ds_scope is
'数据权限范围';

comment on column sys_role.create_by is
'创建人';

comment on column sys_role.update_by is
'修改人';

comment on column sys_role.create_time is
'创建时间';

comment on column sys_role.del_flag is
'删除标记，0未删除，1已删除';

comment on column sys_role.tenant_id is
'租户ID';

/*==============================================================*/
/* Table: sys_role_menu                                       */
/*==============================================================*/
create table sys_role_menu
(
    role_id            INTEGER              not null,
    menu_id            INTEGER              not null,
    constraint PK_SYS_ROLE_MENU primary key (role_id, menu_id)
);

comment on table sys_role_menu is
'角色菜单表';

comment on column sys_role_menu.role_id is
'角色ID';

comment on column sys_role_menu.menu_id is
'菜单ID';

/*==============================================================*/
/* Table: sys_route_conf                                      */
/*==============================================================*/
create table sys_route_conf
(
    id                 INTEGER              not null,
    route_name         VARCHAR2(30)         default NULL,
    route_id           VARCHAR2(30)         default NULL,
    predicates         NCLOB                 default NULL,
    filters            NCLOB                 default NULL,
    uri                VARCHAR2(50)         default NULL,
    sort_order         INTEGER              default 0,
    metadata           NCLOB                 default NULL,
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default sysdate,
    update_time        DATE                 default NULL,
    del_flag           CHAR(1)              default '0',
    constraint PK_SYS_ROUTE_CONF primary key (id)
);

comment on table sys_route_conf is
'路由配置表';

comment on column sys_route_conf.id is
'主键';

comment on column sys_route_conf.predicates is
'断言';

comment on column sys_route_conf.filters is
'过滤器';

comment on column sys_route_conf.sort_order is
'排序';

comment on column sys_route_conf.metadata is
'路由元信息';

comment on column sys_route_conf.create_by is
'创建人';

comment on column sys_route_conf.update_by is
'修改人';

comment on column sys_route_conf.create_time is
'创建时间';

/*==============================================================*/
/* Table: sys_schedule                                        */
/*==============================================================*/
create table sys_schedule
(
    id                 INTEGER              not null,
    title              VARCHAR2(255)        default NULL,
    type               VARCHAR2(255)        default NULL,
    state              VARCHAR2(255)        default NULL,
    content            CLOB,
    time               DATE                 default NULL,
    local_date               DATE                 default NULL,
    create_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default sysdate,
    update_by          VARCHAR2(64)         default ' ' not null,
    update_time        DATE                 default NULL,
    del_flag           CHAR(1)              default '0',
    tenant_id          INTEGER              default NULL,
    constraint PK_SYS_SCHEDULE primary key (id)
);

comment on column sys_schedule.id is
'id';

comment on column sys_schedule.title is
'标题';

comment on column sys_schedule.type is
'日程类型';

comment on column sys_schedule.state is
'状态';

comment on column sys_schedule.content is
'内容';

comment on column sys_schedule.time is
'时间';

comment on column sys_schedule.local_date is
'日期';

comment on column sys_schedule.create_by is
'创建人';

comment on column sys_schedule.create_time is
'创建时间';

comment on column sys_schedule.update_by is
'修改人';

comment on column sys_schedule.del_flag is
'删除标记';

comment on column sys_schedule.tenant_id is
'租户ID';


/*==============================================================*/
/* Table: sys_social_details                                  */
/*==============================================================*/
create table sys_social_details
(
    id                 INTEGER              not null,
    type               VARCHAR2(16)         default NULL,
    remark             VARCHAR2(64)         default NULL,
    app_id             VARCHAR2(64)         default NULL,
    app_secret         VARCHAR2(64)         default NULL,
    redirect_url       VARCHAR2(128)        default NULL,
    ext                VARCHAR2(255)        default NULL,
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default sysdate,
    update_time        DATE                 default sysdate,
    del_flag           CHAR(1)              default '0',
    tenant_id          INTEGER              default 0 not null,
    constraint PK_SYS_SOCIAL_DETAILS primary key (id)
);

comment on table sys_social_details is
'系统社交登录账号表';

comment on column sys_social_details.id is
'主键';

comment on column sys_social_details.type is
'社交登录类型';

comment on column sys_social_details.remark is
'备注';

comment on column sys_social_details.app_id is
'应用ID';

comment on column sys_social_details.app_secret is
'应用密钥';

comment on column sys_social_details.redirect_url is
'回调地址';

comment on column sys_social_details.ext is
'拓展字段';

comment on column sys_social_details.create_by is
'创建人';

comment on column sys_social_details.update_by is
'修改人';

comment on column sys_social_details.create_time is
'创建时间';

comment on column sys_social_details.update_time is
'更新时间';

comment on column sys_social_details.del_flag is
'删除标记，0未删除，1已删除';

comment on column sys_social_details.tenant_id is
'所属租户';

/*==============================================================*/
/* Table: sys_tenant                                          */
/*==============================================================*/
create table sys_tenant
(
    id                 INTEGER              not null,
    name               VARCHAR2(255)        default NULL,
    code               VARCHAR2(64)         default NULL,
    tenant_domain      VARCHAR2(255)        default NULL,
    website_name       VARCHAR2(255)        default NULL,
    mini_qr            VARCHAR2(255)        default NULL,
    background         VARCHAR2(255)        default NULL,
    footer             VARCHAR2(255)        default NULL,
    logo               VARCHAR2(255)        default NULL,
    start_time         DATE                 default NULL,
    end_time           DATE                 default NULL,
    status             CHAR(1)              default '0',
    del_flag           CHAR(1)              default '0',
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default sysdate not null,
    update_time        DATE                 default sysdate not null,
    menu_id            CLOB,
    constraint PK_SYS_TENANT primary key (id)
);

comment on column sys_tenant.id is
'租户ID';

comment on column sys_tenant.name is
'租户名称';

comment on column sys_tenant.code is
'租户编码';

comment on column sys_tenant.tenant_domain is
'租户域名';

comment on column sys_tenant.website_name is
'网站名称';

comment on column sys_tenant.mini_qr is
'移动端二维码';

comment on column sys_tenant.background is
'登录页背景图';

comment on column sys_tenant.footer is
'页脚信息';

comment on column sys_tenant.logo is
'logo';

comment on column sys_tenant.start_time is
'租户开始时间';

comment on column sys_tenant.end_time is
'租户结束时间';

comment on column sys_tenant.status is
'租户状态，0正常，1停用';

comment on column sys_tenant.del_flag is
'删除标记，0未删除，1已删除';

comment on column sys_tenant.create_by is
'创建人';

comment on column sys_tenant.update_by is
'修改人';

comment on column sys_tenant.create_time is
'创建时间';

comment on column sys_tenant.update_time is
'更新时间';

comment on column sys_tenant.menu_id is
'租户菜单ID';

/*==============================================================*/
/* Table: sys_user                                            */
/*==============================================================*/
create table sys_user
(
    user_id            INTEGER              not null,
    username           VARCHAR2(64)         default NULL,
    password           VARCHAR2(255)        default NULL,
    salt               VARCHAR2(255)        default NULL,
    phone              VARCHAR2(20)         default NULL,
    avatar             VARCHAR2(255)        default NULL,
    nickname           VARCHAR2(64)         default NULL,
    name               VARCHAR2(64)         default NULL,
    email              VARCHAR2(128)        default NULL,
    dept_id            INTEGER              default NULL,
    create_by          VARCHAR2(64)         default ' ' not null,
    update_by          VARCHAR2(64)         default ' ' not null,
    create_time        DATE                 default sysdate,
    update_time        DATE                 default NULL,
    lock_flag          CHAR(1)              default '0',
    del_flag           CHAR(1)              default '0',
    wx_openid          VARCHAR2(32)         default NULL,
    mini_openid        VARCHAR2(32)         default NULL,
    qq_openid          VARCHAR2(32)         default NULL,
    gitee_login        VARCHAR2(100)        default NULL,
    osc_id             VARCHAR2(100)        default NULL,
    wx_cp_userid       VARCHAR2(100)        default NULL,
    wx_ding_userid     VARCHAR2(100)        default NULL,
    tenant_id          INTEGER              default 0 not null,
    constraint PK_SYS_USER primary key (user_id)
);

CREATE INDEX user_wx_openid ON sys_user(wx_openid);
CREATE INDEX user_qq_openid ON sys_user(qq_openid);
CREATE INDEX user_idx1_username ON sys_user(username);

comment on table sys_user is
'用户表';

comment on column sys_user.user_id is
'用户ID';

comment on column sys_user.username is
'用户名';

comment on column sys_user.password is
'密码';

comment on column sys_user.salt is
'盐值';

comment on column sys_user.phone is
'电话号码';

comment on column sys_user.avatar is
'头像';

comment on column sys_user.nickname is
'昵称';

comment on column sys_user.name is
'姓名';

comment on column sys_user.email is
'邮箱地址';

comment on column sys_user.dept_id is
'所属部门ID';

comment on column sys_user.create_by is
'创建人';

comment on column sys_user.update_by is
'修改人';

comment on column sys_user.create_time is
'创建时间';

comment on column sys_user.lock_flag is
'锁定标记，0未锁定，9已锁定';

comment on column sys_user.del_flag is
'删除标记，0未删除，1已删除';

comment on column sys_user.wx_openid is
'微信登录openId';

comment on column sys_user.mini_openid is
'小程序openId';

comment on column sys_user.qq_openid is
'QQ openId';

comment on column sys_user.gitee_login is
'码云标识';

comment on column sys_user.osc_id is
'开源中国标识';

comment on column sys_user.wx_cp_userid is
'企业微信唯一ID';

comment on column sys_user.wx_ding_userid is
'钉钉唯一ID';

comment on column sys_user.tenant_id is
'所属租户ID';

/*==============================================================*/
/* Table: sys_user_post                                       */
/*==============================================================*/
create table sys_user_post
(
    user_id            INTEGER              not null,
    post_id            INTEGER              not null,
    constraint PK_SYS_USER_POST primary key (user_id, post_id)
);

comment on column sys_user_post.user_id is
'用户ID';

comment on column sys_user_post.post_id is
'岗位ID';

/*==============================================================*/
/* Table: sys_user_role                                       */
/*==============================================================*/
create table sys_user_role
(
    user_id            INTEGER              not null,
    role_id            INTEGER              not null,
    constraint PK_SYS_USER_ROLE primary key (user_id, role_id)
);

comment on table sys_user_role is
'用户角色表';

comment on column sys_user_role.user_id is
'用户ID';

comment on column sys_user_role.role_id is
'角色ID';

INSERT INTO sys_dept VALUES (1, '总裁办', 1, 'admin', 'admin', sysdate, sysdate, '0', 0, 1);
INSERT INTO sys_dept VALUES (2, '技术部', 2, 'admin', 'admin', sysdate, sysdate, '0', 1, 1);
INSERT INTO sys_dept VALUES (3, '市场部', 3, 'admin', 'admin', sysdate, sysdate, '0', 1, 1);
INSERT INTO sys_dept VALUES (4, '销售部', 4, 'admin', 'admin', sysdate, sysdate, '0', 1, 1);
INSERT INTO sys_dept VALUES (5, '财务部', 5, 'admin', 'admin', sysdate, sysdate, '0', 1, 1);
INSERT INTO sys_dept VALUES (6, '人事行政部', 6, 'admin', 'admin', sysdate, sysdate, '1', 1, 1);
INSERT INTO sys_dept VALUES (7, '研发部', 7, 'admin', 'admin', sysdate, sysdate, '0', 2, 1);
INSERT INTO sys_dept VALUES (8, 'UI设计部', 11, 'admin', 'admin', sysdate, sysdate, '0', 7, 1);
INSERT INTO sys_dept VALUES (9, '产品部', 12, 'admin', 'admin', sysdate, sysdate, '0', 2, 1);
INSERT INTO sys_dept VALUES (10, '渠道部', 13, 'admin', 'admin', sysdate, sysdate, '0', 3, 1);
INSERT INTO sys_dept VALUES (11, '推广部', 14, 'admin', 'admin', sysdate, sysdate, '0', 3, 1);
INSERT INTO sys_dept VALUES (12, '客服部', 15, 'admin', 'admin', sysdate, sysdate, '0', 4, 1);
INSERT INTO sys_dept VALUES (13, '财务会计部', 16, 'admin', 'admin', sysdate, sysdate, '0', 5, 1);
INSERT INTO sys_dept VALUES (14, '审计风控部', 17, 'admin', 'admin', sysdate, sysdate, '0', 5, 1);


INSERT INTO sys_dict VALUES (1, 'log_type', '日志类型', ' ', ' ', sysdate, sysdate, '异常、正常', '1', '0', 1);
INSERT INTO sys_dict VALUES (2, 'social_type', '社交登录', ' ', ' ', sysdate, sysdate, '微信、QQ', '1', '0', 1);
INSERT INTO sys_dict VALUES (3, 'job_type', '定时任务类型', ' ', ' ', sysdate, sysdate, 'quartz', '1', '0', 1);
INSERT INTO sys_dict VALUES (4, 'job_status', '定时任务状态', ' ', ' ', sysdate, sysdate, '发布状态、运行状态', '1', '0', 1);
INSERT INTO sys_dict VALUES (5, 'job_execute_status', '定时任务执行状态', ' ', ' ', sysdate, sysdate, '正常、异常', '1', '0', 1);
INSERT INTO sys_dict VALUES (6, 'misfire_policy', '定时任务错失执行策略', ' ', ' ', sysdate, sysdate, '周期', '1', '0', 1);
INSERT INTO sys_dict VALUES (7, 'gender', '性别', ' ', ' ', sysdate, sysdate, '微信用户性别', '1', '0', 1);
INSERT INTO sys_dict VALUES (8, 'subscribe', '订阅状态', ' ', ' ', sysdate, sysdate, '公众号订阅状态', '1', '0', 1);
INSERT INTO sys_dict VALUES (9, 'response_type', '回复', ' ', ' ', sysdate, sysdate, '微信消息是否已回复', '1', '0', 1);
INSERT INTO sys_dict VALUES (10, 'param_type', '参数配置', ' ', ' ', sysdate, sysdate, '检索、原文、报表、安全、文档、消息、其他', '1', '0', 1);
INSERT INTO sys_dict VALUES (11, 'status_type', '租户状态', ' ', ' ', sysdate, sysdate, '租户状态', '1', '0', 1);
INSERT INTO sys_dict VALUES (12, 'dict_type', '字典类型', ' ', ' ', sysdate, sysdate, '系统类不能修改', '1', '0', 1);
INSERT INTO sys_dict VALUES (13, 'channel_type', '支付类型', ' ', ' ', sysdate, sysdate, '系统类不能修改', '1', '0', 1);
INSERT INTO sys_dict VALUES (14, 'grant_types', '授权类型', ' ', ' ', sysdate, sysdate, NULL, '1', '0', 1);
INSERT INTO sys_dict VALUES (15, 'style_type', '前端风格', ' ', ' ', sysdate, sysdate, '0-Avue 1-element', '1', '0', 1);
INSERT INTO sys_dict VALUES (16, 'captcha_flag_types', '验证码开关', ' ', ' ', sysdate, sysdate, '是否校验验证码', '1', '0', 1);
INSERT INTO sys_dict VALUES (17, 'enc_flag_types', '前端密码加密', ' ', ' ', sysdate, sysdate, '前端密码是否加密传输', '1', '0', 1);
INSERT INTO sys_dict VALUES (18, 'lock_flag', '用户状态', 'admin', ' ', sysdate, NULL, NULL, '1', '0', 1);
INSERT INTO sys_dict VALUES (19, 'ds_config_type', '数据连接类型', 'admin', ' ', sysdate, NULL, NULL, '1', '0', 1);
INSERT INTO sys_dict VALUES (20, 'common_status', '通用状态', 'admin', ' ', sysdate, NULL, NULL, '1', '0', 1);
INSERT INTO sys_dict VALUES (21, 'app_social_type', 'app社交登录', 'admin', ' ', sysdate, NULL, 'app社交登录', '1', '0', 1);
INSERT INTO sys_dict VALUES (22, 'yes_no_type', '是否', 'admin', ' ', sysdate, NULL, NULL, '1', '0', 1);
INSERT INTO sys_dict VALUES (23, 'repType', '微信消息类型', 'admin', ' ', sysdate, NULL, NULL, '0', '0', 1);
INSERT INTO sys_dict VALUES (24, 'leave_status', '请假状态', 'admin', ' ', sysdate, NULL, NULL, '0', '0', 1);
INSERT INTO sys_dict VALUES (25, 'schedule_type', '日程类型', 'admin', ' ', sysdate, NULL, NULL, '0', '0', 1);
INSERT INTO sys_dict VALUES (26, 'schedule_status', '日程状态', 'admin', ' ', sysdate, NULL, NULL, '0', '0', 1);
INSERT INTO sys_dict VALUES (27, 'ds_type', '代码生成器支持的数据库类型', 'admin', ' ', sysdate, NULL, NULL, '1', '0', 1);
INSERT INTO sys_dict VALUES (28, 'message_type', '消息类型', 'admin', ' ', sysdate, NULL, NULL, '1', '0', 1);


INSERT INTO sys_dict_item VALUES (1, 1, '9', '异常', 'log_type', '日志异常', 1, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (2, 1, '0', '正常', 'log_type', '日志正常', 0, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (3, 2, 'WX', '微信', 'social_type', '微信登录', 0, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (4, 2, 'QQ', 'QQ', 'social_type', 'QQ登录', 1, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (5, 3, '1', 'java类', 'job_type', 'java类', 1, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (6, 3, '2', 'spring bean', 'job_type', 'spring bean容器实例', 2, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (7, 3, '9', '其他', 'job_type', '其他类型', 9, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (8, 3, '3', 'Rest 调用', 'job_type', 'Rest 调用', 3, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (9, 3, '4', 'jar', 'job_type', 'jar类型', 4, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (10, 4, '1', '未发布', 'job_status', '未发布', 1, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (11, 4, '2', '运行中', 'job_status', '运行中', 2, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (12, 4, '3', '暂停', 'job_status', '暂停', 3, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (13, 5, '0', '正常', 'job_execute_status', '正常', 0, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (14, 5, '1', '异常', 'job_execute_status', '异常', 1, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (15, 6, '1', '错失周期立即执行', 'misfire_policy', '错失周期立即执行', 1, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (16, 6, '2', '错失周期执行一次', 'misfire_policy', '错失周期执行一次', 2, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (17, 6, '3', '下周期执行', 'misfire_policy', '下周期执行', 3, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (18, 7, '1', '男', 'gender', '微信-男', 0, ' ', ' ', sysdate, sysdate, '微信-男', '0', 1);
INSERT INTO sys_dict_item VALUES (19, 7, '2', '女', 'gender', '女-微信', 1, ' ', ' ', sysdate, sysdate, '女-微信', '0', 1);
INSERT INTO sys_dict_item VALUES (20, 7, '0', '未知', 'gender', '性别未知', 3, ' ', ' ', sysdate, sysdate, '性别未知', '0', 1);
INSERT INTO sys_dict_item VALUES (21, 8, '0', '未关注', 'subscribe', '公众号-未关注', 0, ' ', ' ', sysdate, sysdate, '公众号-未关注', '0', 1);
INSERT INTO sys_dict_item VALUES (22, 8, '1', '已关注', 'subscribe', '公众号-已关注', 1, ' ', ' ', sysdate, sysdate, '公众号-已关注', '0', 1);
INSERT INTO sys_dict_item VALUES (23, 9, '0', '未回复', 'response_type', '微信消息-未回复', 0, ' ', ' ', sysdate, sysdate, '微信消息-未回复', '0', 1);
INSERT INTO sys_dict_item VALUES (24, 9, '1', '已回复', 'response_type', '微信消息-已回复', 1, ' ', ' ', sysdate, sysdate, '微信消息-已回复', '0', 1);
INSERT INTO sys_dict_item VALUES (25, 10, '1', '检索', 'param_type', '检索', 0, ' ', ' ', sysdate, sysdate, '检索', '0', 1);
INSERT INTO sys_dict_item VALUES (26, 10, '2', '原文', 'param_type', '原文', 0, ' ', ' ', sysdate, sysdate, '原文', '0', 1);
INSERT INTO sys_dict_item VALUES (27, 10, '3', '报表', 'param_type', '报表', 0, ' ', ' ', sysdate, sysdate, '报表', '0', 1);
INSERT INTO sys_dict_item VALUES (28, 10, '4', '安全', 'param_type', '安全', 0, ' ', ' ', sysdate, sysdate, '安全', '0', 1);
INSERT INTO sys_dict_item VALUES (29, 10, '5', '文档', 'param_type', '文档', 0, ' ', ' ', sysdate, sysdate, '文档', '0', 1);
INSERT INTO sys_dict_item VALUES (30, 10, '6', '消息', 'param_type', '消息', 0, ' ', ' ', sysdate, sysdate, '消息', '0', 1);
INSERT INTO sys_dict_item VALUES (31, 10, '9', '其他', 'param_type', '其他', 0, ' ', ' ', sysdate, sysdate, '其他', '0', 1);
INSERT INTO sys_dict_item VALUES (32, 10, '0', '默认', 'param_type', '默认', 0, ' ', ' ', sysdate, sysdate, '默认', '0', 1);
INSERT INTO sys_dict_item VALUES (33, 11, '0', '正常', 'status_type', '状态正常', 0, ' ', ' ', sysdate, sysdate, '状态正常', '0', 1);
INSERT INTO sys_dict_item VALUES (34, 11, '9', '冻结', 'status_type', '状态冻结', 1, ' ', ' ', sysdate, sysdate, '状态冻结', '0', 1);
INSERT INTO sys_dict_item VALUES (35, 12, '1', '系统类', 'dict_type', '系统类字典', 0, ' ', ' ', sysdate, sysdate, '不能修改删除', '0', 1);
INSERT INTO sys_dict_item VALUES (36, 12, '0', '业务类', 'dict_type', '业务类字典', 0, ' ', ' ', sysdate, sysdate, '可以修改', '0', 1);
INSERT INTO sys_dict_item VALUES (37, 2, 'GITEE', '码云', 'social_type', '码云', 2, ' ', ' ', sysdate, sysdate, '码云', '0', 1);
INSERT INTO sys_dict_item VALUES (38, 2, 'OSC', '开源中国', 'social_type', '开源中国登录', 2, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (39, 14, 'password', '密码模式', 'grant_types', '支持oauth密码模式', 0, ' ', ' ', sysdate, sysdate, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (40, 14, 'authorization_code', '授权码模式', 'grant_types', 'oauth2 授权码模式', 1, ' ', ' ', sysdate, sysdate, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (41, 14, 'client_credentials', '客户端模式', 'grant_types', 'oauth2 客户端模式', 2, ' ', ' ', sysdate, sysdate, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (42, 14, 'refresh_token', '刷新模式', 'grant_types', 'oauth2 刷新token', 3, ' ', ' ', sysdate, sysdate, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (43, 14, 'implicit', '简化模式', 'grant_types', 'oauth2 简化模式', 4, ' ', ' ', sysdate, sysdate, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (44, 15, '0', 'Avue', 'style_type', 'Avue风格', 0, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (45, 15, '1', 'element', 'style_type', 'element-ui', 1, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (46, 16, '0', '关', 'captcha_flag_types', '不校验验证码', 0, ' ', ' ', sysdate, sysdate, '不校验验证码 -0', '0', 1);
INSERT INTO sys_dict_item VALUES (47, 16, '1', '开', 'captcha_flag_types', '校验验证码', 1, ' ', ' ', sysdate, sysdate, '不校验验证码-1', '0', 1);
INSERT INTO sys_dict_item VALUES (48, 17, '0', '否', 'enc_flag_types', '不加密', 0, ' ', ' ', sysdate, sysdate, '不加密-0', '0', 1);
INSERT INTO sys_dict_item VALUES (49, 17, '1', '是', 'enc_flag_types', '加密', 1, ' ', ' ', sysdate, sysdate, '加密-1', '0', 1);
INSERT INTO sys_dict_item VALUES (50, 13, 'MERGE_PAY', '聚合支付', 'channel_type', '聚合支付', 1, ' ', ' ', sysdate, sysdate, '聚合支付', '0', 1);
INSERT INTO sys_dict_item VALUES (51, 2, 'CAS', 'CAS登录', 'social_type', 'CAS 单点登录系统', 3, ' ', ' ', sysdate, sysdate, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (52, 2, 'DINGTALK', '钉钉', 'social_type', '钉钉', 3, ' ', ' ', sysdate, sysdate, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (53, 2, 'WEIXIN_CP', '企业微信', 'social_type', '企业微信', 3, ' ', ' ', sysdate, sysdate, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (54, 15, '2', 'APP', 'style_type', 'uview风格', 1, ' ', ' ', sysdate, sysdate, '', '0', 1);
INSERT INTO sys_dict_item VALUES (55, 13, 'ALIPAY_WAP', '支付宝支付', 'channel_type', '支付宝支付', 1, ' ', ' ', sysdate, sysdate, '聚合支付', '0', 1);
INSERT INTO sys_dict_item VALUES (56, 13, 'WEIXIN_MP', '微信支付', 'channel_type', '微信支付', 1, ' ', ' ', sysdate, sysdate, '聚合支付', '0', 1);
INSERT INTO sys_dict_item VALUES (57, 14, 'mobile', 'mobile', 'grant_types', '移动端登录', 5, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (58, 18, '0', '有效', 'lock_flag', '有效', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (59, 18, '9', '禁用', 'lock_flag', '禁用', 1, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (60, 15, '4', 'vue3', 'style_type', 'element-plus', 4, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (61, 19, '0', '主机', 'ds_config_type', '主机', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (62, 19, '1', 'JDBC', 'ds_config_type', 'jdbc', 2, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (63, 20, 'false', '否', 'common_status', '否', 1, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (64, 20, 'true', '是', 'common_status', '是', 2, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (65, 21, 'MINI', '小程序', 'app_social_type', '小程序登录', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (66, 22, '0', '否', 'yes_no_type', '0', 0, 'admin', ' ', sysdate, NULL, '0', '0', 1);
INSERT INTO sys_dict_item VALUES (67, 22, '1', '是', 'yes_no_type', '1', 0, 'admin', ' ', sysdate, NULL, '1', '0', 1);
INSERT INTO sys_dict_item VALUES (69, 23, 'text', '文本', 'repType', '文本', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (70, 23, 'image', '图片', 'repType', '图片', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (71, 23, 'voice', '语音', 'repType', '语音', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (72, 23, 'video', '视频', 'repType', '视频', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (73, 23, 'shortvideo', '小视频', 'repType', '小视频', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (74, 23, 'location', '地理位置', 'repType', '地理位置', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (75, 23, 'link', '链接消息', 'repType', '链接消息', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (76, 23, 'event', '事件推送', 'repType', '事件推送', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (77, 24, '0', '未提交', 'leave_status', '未提交', 0, 'admin', ' ', sysdate, NULL, '未提交', '0', 1);
INSERT INTO sys_dict_item VALUES (78, 24, '1', '审批中', 'leave_status', '审批中', 0, 'admin', ' ', sysdate, NULL, '审批中', '0', 1);
INSERT INTO sys_dict_item VALUES (79, 24, '2', '完成', 'leave_status', '完成', 0, 'admin', ' ', sysdate, NULL, '完成', '0', 1);
INSERT INTO sys_dict_item VALUES (80, 24, '9', '驳回', 'leave_status', '驳回', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (81, 25, 'record', '日程记录', 'schedule_type', '日程记录', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (82, 25, 'plan', '计划', 'schedule_type', '计划类型', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (83, 26, '0', '计划中', 'schedule_status', '日程状态', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (84, 26, '1', '已开始', 'schedule_status', '已开始', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (85, 26, '3', '已结束', 'schedule_status', '已结束', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (86, 27, 'mysql', 'mysql', 'ds_type', 'mysql', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (87, 27, 'pg', 'pg', 'ds_type', 'pg', 1, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (88, 27, 'oracle', 'oracle', 'ds_type', 'oracle', 2, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (89, 27, 'mssql', 'mssql', 'ds_type', 'mssql', 3, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (90, 27, 'db2', 'db2', 'ds_type', 'db2', 4, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (91, 27, 'dm', '达梦', 'ds_type', '达梦', 5, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (92, 27, 'highgo', '瀚高', 'ds_type', '瀚高数据库', 5, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (93, 28, '0', '公告', 'message_type', '主页公告显示', 0, 'admin', ' ', sysdate, NULL, NULL, '0', 1);
INSERT INTO sys_dict_item VALUES (94, 28, '1', '站内信', 'message_type', '右上角显示', 1, 'admin', ' ', sysdate, NULL, NULL, '0', 1);


INSERT INTO sys_i18n VALUES (1, 'router.permissionManagement', '权限管理', 'Permission Management', ' ', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (2, 'router.userManagement', '用户管理', 'User Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (3, 'router.menuManagement', '菜单管理', 'Menu Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (4, 'router.roleManagement', '角色管理', 'Role Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (5, 'router.departmentManagement', '部门管理', 'Department Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (6, 'router.tenantManagement', '租户管理', 'Tenant Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (7, 'router.postManagement', '岗位管理', 'Post Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (8, 'router.systemManagement', '系统管理', 'System Management', 'admin', sysdate, 'admin', sysdate, '0');
INSERT INTO sys_i18n VALUES (9, 'router.operationLog', '操作日志', 'Operation Log', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (10, 'router.dictManagement', '字典管理', 'Dictionary Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (11, 'router.parameterManagement', '参数管理', 'Parameter Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (12, 'router.codeGeneration', '代码生成', 'Code Generation', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (13, 'router.terminalManagement', '终端管理', 'Terminal Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (14, 'router.keyManagement', '密钥管理', 'Key Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (15, 'router.tokenManagement', '令牌管理', 'Token Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (16, 'router.quartzManagement', 'Quartz管理', 'Quartz Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (17, 'router.metadataManagement', '元数据管理', 'Metadata Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (18, 'router.documentExtension', '文档扩展', 'Document Extension', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (19, 'router.fileManagement', '文件管理', 'File Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (20, 'router.platformDevelopment', '开发平台', 'Platform Development', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (21, 'router.dataSourceManagement', '数据源管理', 'Data Source Management', 'admin', sysdate, 'admin', sysdate, '0');
INSERT INTO sys_i18n VALUES (22, 'router.formDesign', '表单设计', 'Form Design', 'admin', sysdate, 'admin', sysdate, '0');
INSERT INTO sys_i18n VALUES (23, 'router.appManagement', 'APP管理', 'App Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (24, 'router.customerManagement', '客户管理', 'Customer Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (25, 'router.appRole', 'APP角色', 'App Role', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (26, 'router.appPermission', 'APP权限', 'App Permission', 'admin', sysdate, 'admin', sysdate, '0');
INSERT INTO sys_i18n VALUES (27, 'router.appKey', 'APP秘钥', 'App Key', 'admin', sysdate, 'admin', sysdate, '0');
INSERT INTO sys_i18n VALUES (28, 'router.internationalizationManagement', '国际化管理', 'Internationalization Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (29, 'router.auditLog', '审计日志', 'Audit Log', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (30, 'router.systemMonitoring', '系统监控', 'System Monitoring', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (31, 'router.generatePages', '生成页面', 'Generate Pages', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (32, 'router.templateManagement', '模板管理', 'Template Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (33, 'router.templateGroup', '模板分组', 'Template Group', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (34, 'router.fieldManagement', '字段管理', 'Field Management', 'admin', sysdate, 'admin', sysdate, '0');
INSERT INTO sys_i18n VALUES (35, 'router.wechatPlatform', '公众号平台', 'WeChat Platform', 'admin', sysdate, 'admin', sysdate, '0');
INSERT INTO sys_i18n VALUES (36, 'router.accountManagement', '账号管理', 'Account Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (37, 'router.menuSettings', '菜单设置', 'Menu Settings', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (38, 'router.fanManagement', '粉丝管理', 'Fan Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (39, 'router.messageManagement', '消息管理', 'Message Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (40, 'router.paymentSystem', '支付系统', 'Payment System', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (41, 'router.checkoutCounter', '收银台', 'Checkout Counter', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (42, 'router.mediaManagement', '素材管理', 'Media Management', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (43, 'router.paymentChannel', '支付渠道', 'Payment Channel', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (44, 'router.productOrder', '商品订单', 'Product Order', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (45, 'router.notificationRecord', '通知记录', 'Notification Record', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (46, 'router.refundOrder', '退款订单', 'Refund Order', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (47, 'router.paymentOrder', '支付订单', 'Payment Order', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (48, 'router.autoReply', '自动回复', 'Auto Reply', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (49, 'router.operationalData', '运营数据', 'Operational Data', 'admin', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (50, 'router.logManagement', '日志管理', 'Log Management', 'admin', NULL, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (51, 'router.collaborativeOffice', '协同办公', 'Collaborative Office', 'admin', NULL, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (52, 'router.modelManagement', '模型管理', 'Model Management', 'admin', NULL, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (53, 'router.modelDiagramView', '模型图查看', 'Model Diagram View', 'admin', NULL, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (54, 'router.processManagement', '流程管理', 'Process Management', 'admin', NULL, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (55, 'router.leaveWorkOrder', '请假工单', 'Leave Work Order', 'admin', NULL, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (56, 'router.todoTask', '代办任务', 'Todo Task', 'admin', NULL, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (57, 'router.tagManagement', '标签管理', 'Tag Management', 'admin', NULL, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (58, 'router.articleInformation', '文章资讯', 'Article Information', ' ', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (59, 'router.articleCategory', '文章分类', 'Article Category', ' ', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (60, 'router.interfaceSettings', '界面设置', 'Interface Settings', ' ', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (61, 'router.bottomNavigation', '底部导航', 'Bottom Navigation', ' ', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (62, 'router.cacheMonitoring', '缓存监控', 'Cache Monitoring', ' ', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (63, 'rotuer. initiateProcess', '发起流程', 'Initiate Process', ' ', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (64, 'router.taskManagement', '任务管理', 'Task Management', ' ', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (65, 'router.myInitiations', '我的发起', 'My Initiations', ' ', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (66, 'router.copiedtoMe', '抄送给我', 'Copied to Me', ' ', sysdate, ' ', NULL, '0');
INSERT INTO sys_i18n VALUES (67, 'router.completedTasks', '我的已办', 'Completed Tasks', ' ', sysdate, ' ', sysdate, '0');
INSERT INTO sys_i18n VALUES (68, 'router.bizPlatform', '业务平台', 'Biz Platform', ' ', sysdate, ' ', sysdate, '0');
INSERT INTO sys_i18n VALUES (69, 'router.baseTools', '基础工具', 'Base Tools', ' ', sysdate, ' ', sysdate, '0');
INSERT INTO sys_i18n VALUES (70, 'router.route', '路由管理', 'Route Management', ' ', sysdate, ' ', sysdate, '0');
INSERT INTO sys_i18n VALUES (71, 'router.datav', '大屏看板', 'Data Visual', ' ', sysdate, ' ', sysdate, '0');
INSERT INTO sys_i18n VALUES (72, 'router.bi', '数据报表', 'Bi Report', ' ', sysdate, ' ', sysdate, '0');
INSERT INTO sys_i18n VALUES (73, 'router.message', '站内信管理', 'Message', ' ', sysdate, ' ', sysdate, '0');


INSERT INTO sys_menu VALUES (1000, '权限管理', NULL, '/system', 2000, 'iconfont icon-icon-', '1', 0, '0', '0', '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1100, '用户管理', NULL, '/admin/system/user/index', 1000, 'ele-User', '1', 1, '0', '0', '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1101, '用户新增', 'sys_user_add', NULL, 1100, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1102, '用户修改', 'sys_user_edit', NULL, 1100, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1103, '用户删除', 'sys_user_del', NULL, 1100, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1104, '导入导出', 'sys_user_export', NULL, 1100, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1200, '菜单管理', NULL, '/admin/system/menu/index', 1000, 'iconfont icon-caidan', '1', 2, '0', '0', '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1201, '菜单新增', 'sys_menu_add', NULL, 1200, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1202, '菜单修改', 'sys_menu_edit', NULL, 1200, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1203, '菜单删除', 'sys_menu_del', NULL, 1200, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1300, '角色管理', NULL, '/admin/system/role/index', 1000, 'iconfont icon-gerenzhongxin', '1', 3, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1301, '角色新增', 'sys_role_add', NULL, 1300, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1302, '角色修改', 'sys_role_edit', NULL, 1300, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1303, '角色删除', 'sys_role_del', NULL, 1300, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1304, '分配权限', 'sys_role_perm', NULL, 1300, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1305, '角色导入导出', 'sys_role_export', NULL, 1300, NULL, '1', 4, '0', NULL, '1', ' ', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (1400, '部门管理', NULL, '/admin/system/dept/index', 1000, 'iconfont icon-zidingyibuju', '1', 4, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1401, '部门新增', 'sys_dept_add', NULL, 1400, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1402, '部门修改', 'sys_dept_edit', NULL, 1400, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1403, '部门删除', 'sys_dept_del', NULL, 1400, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1404, '开放互联', 'sys_connect_sync', NULL, 1400, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1500, '租户管理', NULL, '/admin/system/tenant/index', 1000, 'iconfont icon-shuxingtu', '1', 9, '0', '0', '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1501, '租户新增', 'sys_systenant_add', NULL, 1500, '1', '1', 0, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1502, '租户修改', 'sys_systenant_edit', NULL, 1500, '1', '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1503, '租户删除', 'sys_systenant_del', NULL, 1500, '1', '1', 2, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1504, '租户套餐', 'sys_systenant_tenantmenu', NULL, 1500, '1', '1', 1, '0', NULL, '1', 'admin', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1505, '租户套餐删除', 'sys_systenantmenu_del', NULL, 1500, '1', '1', 1, '0', NULL, '1', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1506, '租户套餐编辑', 'sys_systenantmenu_edit', NULL, 1500, '1', '1', 1, '0', NULL, '1', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1507, '租户套餐新增', 'sys_systenantmenu_add', NULL, 1500, '1', '1', 1, '0', NULL, '1', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1508, '租户套餐导出', 'sys_systenant_export', NULL, 1500, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (1600, '岗位管理', NULL, '/admin/system/post/index', 1000, 'iconfont icon--chaifenhang', '1', 5, '1', '0', '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1601, '岗位信息查看', 'sys_post_view', NULL, 1600, NULL, '1', 0, '0', NULL, '1', ' ', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (1602, '岗位信息新增', 'sys_post_add', NULL, 1600, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (1603, '岗位信息修改', 'sys_post_edit', NULL, 1600, NULL, '1', 2, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (1604, '岗位信息删除', 'sys_post_del', NULL, 1600, NULL, '1', 3, '0', NULL, '1', ' ', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (1605, '岗位导入导出', 'sys_post_export', NULL, 1600, NULL, '1', 4, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2000, '系统管理', NULL, '/admin', -1, 'iconfont icon-quanjushezhi_o', '1', 1, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2001, '日志管理', NULL, '/admin/logs', 2000, 'ele-Cloudy', '1', 1, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2100, '操作日志', NULL, '/admin/log/index', 2001, 'iconfont icon-jinridaiban', '1', 2, '0', '0', '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2101, '日志删除', 'sys_log_del', NULL, 2100, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2102, '导入导出', 'sys_log_export', NULL, 2100, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2103, '审计日志', NULL, '/admin/audit/index', 2001, 'iconfont icon-biaodan', '1', 1, '0', '0', '0', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2104, '审计记录表删除', 'sys_audit_del', NULL, 2103, '1', '1', 3, '0', NULL, '1', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2105, '导入导出', 'sys_audit_export', NULL, 2103, '1', '1', 3, '0', NULL, '1', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2200, '字典管理', NULL, '/admin/dict/index', 2000, 'iconfont icon-zhongduancanshuchaxun', '1', 6, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2201, '字典删除', 'sys_dict_del', NULL, 2200, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2202, '字典新增', 'sys_dict_add', NULL, 2200, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2203, '字典修改', 'sys_dict_edit', NULL, 2200, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2210, '参数管理', NULL, '/admin/param/index', 2000, 'iconfont icon-wenducanshu-05', '1', 7, '1', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2211, '参数新增', 'sys_syspublicparam_add', NULL, 2210, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2212, '参数删除', 'sys_syspublicparam_del', NULL, 2210, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2213, '参数编辑', 'sys_syspublicparam_edit', NULL, 2210, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2300, '代码生成', NULL, '/gen/table/index', 9000, 'iconfont icon-zhongduancanshu', '1', 1, '0', '0', '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2400, '终端管理', NULL, '/admin/client/index', 2000, 'iconfont icon-gongju', '1', 9, '1', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2401, '客户端新增', 'sys_client_add', NULL, 2400, '1', '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2402, '客户端修改', 'sys_client_edit', NULL, 2400, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2403, '客户端删除', 'sys_client_del', NULL, 2400, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2500, '密钥管理', NULL, '/admin/social/index', 2000, 'iconfont icon-quanxian', '1', 10, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2501, '密钥新增', 'sys_social_details_add', NULL, 2500, '1', '1', 0, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2502, '密钥修改', 'sys_social_details_edit', NULL, 2500, '1', '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2503, '密钥删除', 'sys_social_details_del', NULL, 2500, '1', '1', 2, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2600, '令牌管理', NULL, '/admin/token/index', 2000, 'ele-Key', '1', 11, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2601, '令牌删除', 'sys_token_del', NULL, 2600, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2800, 'Quartz管理', NULL, '/tools/job-manage/index', 9910, 'ele-AlarmClock', '1', 4, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2810, '任务新增', 'job_sys_job_add', NULL, 2800, '1', '1', 0, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2820, '任务修改', 'job_sys_job_edit', NULL, 2800, '1', '1', 0, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2830, '任务删除', 'job_sys_job_del', NULL, 2800, '1', '1', 0, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2840, '任务暂停', 'job_sys_job_shutdown_job', NULL, 2800, '1', '1', 0, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2850, '任务开始', 'job_sys_job_start_job', NULL, 2800, '1', '1', 0, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2860, '任务刷新', 'job_sys_job_refresh_job', NULL, 2800, '1', '1', 0, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2870, '执行任务', 'job_sys_job_run_job', NULL, 2800, '1', '1', 0, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2871, '导出', 'job_sys_job_export', NULL, 2800, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (2900, '国际化管理', NULL, '/admin/i18n/index', 2000, 'iconfont icon-zhongyingzhuanhuan', '1', 8, '0', NULL, '0', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2901, '系统表-国际化查看', 'sys_i18n_view', NULL, 2900, '1', '1', 0, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (2902, '系统表-国际化新增', 'sys_i18n_add', NULL, 2900, '1', '1', 1, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (2903, '系统表-国际化修改', 'sys_i18n_edit', NULL, 2900, '1', '1', 2, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (2904, '系统表-国际化删除', 'sys_i18n_del', NULL, 2900, '1', '1', 3, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (2905, '导入导出', 'sys_i18n_export', NULL, 2900, '1', '1', 3, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (2906, '文件管理', NULL, '/admin/file/index', 2000, 'ele-Files', '1', 6, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (2907, '删除文件', 'sys_file_del', NULL, 2906, NULL, '1', 1, '0', NULL, '1', ' ', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3000, '公众号平台', NULL, '/mp', 9900, 'iconfont icon-putong', '1', 3, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3001, '账号管理', NULL, '/biz/mp/wx-account/index', 3000, 'iconfont icon-putong', '1', 0, '0', '0', '0', 'admin', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3002, '菜单设置', NULL, '/biz/mp/wx-menu/index', 3000, 'iconfont icon--chaifenlie', '1', 1, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3003, '删除', 'mp_wxaccount_del', NULL, 3001, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3004, '新增', 'mp_wxaccount_add', NULL, 3001, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3005, '编辑', 'mp_wxaccount_edit', NULL, 3001, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3006, '粉丝管理', NULL, '/biz/mp/wx-account-fans/index', 3000, 'iconfont icon-tongzhi3', '1', 2, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3007, '同步粉丝', 'mp_wxaccountfans_sync', NULL, 3006, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3008, '消息管理', NULL, '/biz/mp/wx-fans-msg/index', 3000, 'iconfont icon-tongzhi3', '1', 6, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3009, '修改微信消息', 'mp_wxmsg_edit', NULL, 3008, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3010, '标签管理', NULL, '/biz/mp/wx-account-tag/index', 3000, 'iconfont icon-zidingyibuju', '1', 3, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3011, '新增标签', 'mp_wx_account_tag_add', NULL, 3010, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3012, '编辑标签', 'mp_wx_account_tag_edit', NULL, 3010, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3013, '标签删除', 'mp_wx_account_tag_del', NULL, 3010, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3014, '同步标签', 'mp_wx_account_tag_sync', NULL, 3010, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3015, '素材管理', NULL, '/biz/mp/wx-material/index', 3000, 'iconfont icon-tongzhi3', '1', 5, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3016, '素材维护', 'mp_wxmaterial_add', NULL, 3015, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3017, '素材删除', 'mp_wxmaterial_del', NULL, 3015, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3018, '自动回复', NULL, '/biz/mp/wx-auto-reply/index', 3000, 'iconfont icon-putong', '1', 4, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3019, '新增回复', 'mp_wxautoreply_add', NULL, 3018, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3020, '编辑回复', 'mp_wxautoreply_edit', NULL, 3018, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3021, '删除回复', 'mp_wxautoreply_del', NULL, 3018, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3022, '运营数据', NULL, '/biz/mp/wx-statistics/index', 3000, 'iconfont icon-shuxing', '1', 8, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (3023, '新增消息', 'mp_wxmsg_add', NULL, 3008, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3024, '新增粉丝', 'mp_wxaccountfans_add', 'mp_wxaccountfans_add', 3006, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3025, '粉丝编辑', 'mp_wxaccountfans_edit', 'mp_wxaccountfans_add', 3006, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3026, '粉丝删除', 'mp_wxaccountfans_del', 'mp_wxaccountfans_add', 3006, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3027, '新增菜单', 'mp_wxmenu_add', NULL, 3002, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3028, '发布菜单', 'mp_wxmenu_push', NULL, 3002, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (3029, '删除菜单', 'mp_wxmenu_del', NULL, 3002, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (4000, '系统监控', NULL, '/daemon', -1, 'iconfont icon-shuju', '1', 3, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '1', 1);
INSERT INTO sys_menu VALUES (4001, '文档扩展', NULL, 'http://pigx-gateway:9999/doc.html', 9910, 'iconfont icon-biaodan', '1', 2, '0', '1', '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (4002, '缓存监控', NULL, '/tools/data/cache', 9910, 'iconfont icon-shuju', '1', 1, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (4010, '站内信管理', '', '/tools/message/index', 9910, 'iconfont icon-zhongduancanshuchaxun', '1', 7, '0', NULL, '0', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (4011, '站内信息查看', 'sys_message_view', NULL, 4010, '1', '1', 0, '0', NULL, '1', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (4012, '站内信息新增', 'sys_message_add', NULL, 4010, '1', '1', 1, '0', NULL, '1', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (4013, '站内信息修改', 'sys_message_edit', NULL, 4010, '1', '1', 2, '0', NULL, '1', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (4014, '站内信息删除', 'sys_message_del', NULL, 4010, '1', '1', 3, '0', NULL, '1', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (5000, '支付系统', NULL, '/pay', 9900, 'iconfont icon-neiqianshujuchucun', '1', 1, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (5001, '收银台', NULL, '/biz/pay/cd/index', 5000, 'iconfont icon-diqiu1', '1', 0, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (5002, '支付渠道', NULL, '/biz/pay/channel/index', 5000, 'iconfont icon-crew_feature', '1', 1, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (5003, '查询', 'pay_channel_view', NULL, 5002, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5004, '新增', 'pay_channel_add', NULL, 5002, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5005, '编辑', 'pay_channel_edit', NULL, 5002, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5006, '删除', 'pay_channel_del', NULL, 5002, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5007, '导出', 'pay_channel_export', NULL, 5002, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5008, '商品订单', NULL, '/biz/pay/order/index', 5000, 'iconfont icon-fuwenbenkuang', '1', 2, '0', '0', '0', 'admin', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (5009, '新增', 'pay_order_add', NULL, 5008, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5010, '删除', 'pay_order_del', NULL, 5008, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5011, '修改', 'pay_order_edit', NULL, 5008, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5012, '查找', 'pay_order_view', NULL, 5008, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5013, '导出', 'pay_order_export', NULL, 5008, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5014, '通知记录', NULL, '/biz/pay/record/index', 5000, 'iconfont icon-fuwenbenkuang', '1', 5, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (5015, '新增', 'pay_record_add', NULL, 5014, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5016, '修改', 'pay_record_edit', NULL, 5014, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5017, '删除', 'pay_record_del', NULL, 5014, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5018, '导出', 'pay_record_export', NULL, 5014, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5019, '查询', 'pay_record_view', NULL, 5014, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5020, '退款订单', NULL, '/biz/pay/refund/index', 5000, 'iconfont icon-fuwenbenkuang', '1', 4, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (5021, '查询', 'pay_refund_view', NULL, 5020, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5022, '新增', 'pay_refund_add', NULL, 5020, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5023, '修改', 'pay_refund_edit', NULL, 5020, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5024, '删除', 'pay_refund_del', NULL, 5020, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5025, '导出', 'pay_refund_export', NULL, 5020, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (5026, '支付订单', NULL, '/biz/pay/trade/index', 5000, 'iconfont icon-biaodan', '1', 3, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (5027, '查询', 'pay_trade_view', NULL, 5026, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5028, '新增', 'pay_trade_add', NULL, 5026, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5029, '修改', 'pay_trade_edit', NULL, 5026, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5030, '删除', 'pay_trade_del', NULL, 5026, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (5031, '导出', 'pay_trade_export', NULL, 5026, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (6000, '协同办公', NULL, '/flow', -1, 'ele-Present', '1', 4, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (6001, '流程管理', NULL, '/flow/group/index', 6000, 'iconfont icon-gongju', '1', 3, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (6002, '创建流程', NULL, '/flow/create/all', 6000, 'fa fa-arrow-circle-right', '0', 2, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (6003, '发起流程', NULL, '/flow/list/index', 6000, 'fa fa-play', '1', 1, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (6004, '任务管理', NULL, '/task', 6000, 'fa fa-th', '1', 0, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (6005, '代办任务', NULL, '/flow/task/pending', 6004, 'fa fa-flag-checkered', '1', 0, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (6006, '我的已办', NULL, '/flow/task/completed', 6004, 'fa fa-hand-o-right', '1', 3, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (6007, '我的发起', NULL, '/flow/task/started', 6004, 'fa fa-plane', '1', 1, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (6008, '抄送给我', NULL, '/flow/task/cc', 6004, 'fa fa-arrow-circle-right', '1', 2, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7000, 'APP管理', NULL, '/app', 9900, 'ele-Cellphone', '1', 2, '0', '0', '0', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7100, '客户管理', NULL, '/biz/app/appuser/index', 7000, 'ele-UserFilled', '1', 1, '1', NULL, '0', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7101, '新增用户', 'app_appuser_add', NULL, 7100, NULL, '1', 1, '0', NULL, '1', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7102, '编辑用户', 'app_appuser_edit', NULL, 7100, NULL, '1', 1, '0', NULL, '1', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7103, '删除用户', 'app_appuser_del', NULL, 7100, NULL, '1', 1, '0', NULL, '1', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7104, '导出用户', 'app_appuser_export', NULL, 7100, NULL, '1', 1, '0', NULL, '1', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7200, 'APP角色', NULL, '/biz/app/approle/index', 7000, 'ele-Stamp', '1', 2, '0', '0', '0', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7201, '删除角色', 'app_approle_del', NULL, 7200, NULL, '1', 1, '0', NULL, '1', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7202, '编辑角色', 'app_approle_edit', NULL, 7200, NULL, '1', 1, '0', NULL, '1', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7203, '新增角色', 'app_approle_add', NULL, 7200, NULL, '1', 1, '0', NULL, '1', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7204, '导出角色', 'app_approle_export', NULL, 7200, NULL, '1', 1, '0', NULL, '1', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7300, 'APP秘钥', NULL, '/biz/app/appsocial/index', 7000, 'iconfont icon-quanxian', '1', 3, '0', '0', '0', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7301, '删除秘钥', 'app_social_details_del', NULL, 7300, NULL, '1', 1, '0', NULL, '1', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7302, '修改秘钥', 'app_social_details_edit', NULL, 7300, NULL, '1', 1, '0', NULL, '1', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7303, '保存秘钥', 'app_social_details_add', NULL, 7300, NULL, '1', 1, '0', NULL, '1', 'admin', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7400, '文章资讯', '', '/biz/app/appArticle/index', 7000, 'ele-CollectionTag', '1', 4, '0', NULL, '0', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7401, '文章资讯表查看', 'app_appArticle_view', NULL, 7400, '1', '1', 0, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (7402, '文章资讯表新增', 'app_appArticle_add', NULL, 7400, '1', '1', 1, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (7403, '文章资讯表修改', 'app_appArticle_edit', NULL, 7400, '1', '1', 2, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (7404, '文章资讯表删除', 'app_appArticle_del', NULL, 7400, '1', '1', 3, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (7405, '导入导出', 'app_appArticle_export', NULL, 7400, '1', '1', 3, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (7500, '文章分类', '', '/biz/app/appArticleCategory/index', 7000, 'iconfont icon-caidan', '1', 5, '0', NULL, '0', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7501, '文章分类表查看', 'app_appArticleCategory_view', NULL, 7500, '1', '1', 0, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (7502, '文章分类表新增', 'app_appArticleCategory_add', NULL, 7500, '1', '1', 1, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (7503, '文章分类表修改', 'app_appArticleCategory_edit', NULL, 7500, '1', '1', 2, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (7504, '文章分类表删除', 'app_appArticleCategory_del', NULL, 7500, '1', '1', 3, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (7505, '导入导出', 'app_appArticleCategory_export', NULL, 7500, '1', '1', 3, '0', NULL, '1', ' ', NULL, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (7600, '文章发布', NULL, '/biz/app/appArticle/form', 7000, 'iconfont icon-shuaxin', '0', 4, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7700, '界面设置', '', '/biz/app/page/index', 7000, 'iconfont icon-diannao1', '1', 8, '0', NULL, '0', ' ', NULL, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (7701, '底部导航', NULL, '/biz/app/tabbar/index', 7000, 'iconfont icon-neiqianshujuchucun', '1', 9, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9000, '开发平台', NULL, '/gen', -1, 'iconfont icon-shuxingtu', '1', 9, '0', '0', '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9005, '数据源管理', NULL, '/gen/datasource/index', 9000, 'ele-Coin', '1', 0, '0', NULL, '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9006, '表单设计', NULL, '/gen/design/index', 9000, 'iconfont icon-AIshiyanshi', '0', 2, '0', '0', '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9007, '生成页面', NULL, '/gen/gener/index', 9000, 'iconfont icon-tongzhi4', '0', 1, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9050, '元数据管理', NULL, '/gen/metadata', 9000, 'iconfont icon--chaifenhang', '1', 9, '0', '0', '0', ' ', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9051, '模板管理', NULL, '/gen/template/index', 9050, 'iconfont icon--chaifenhang', '1', 5, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9052, '查询', 'codegen_template_view', NULL, 9051, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9053, '增加', 'codegen_template_add', NULL, 9051, NULL, '1', 0, '0', '0', '1', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9054, '新增', 'codegen_template_add', NULL, 9051, NULL, '0', 1, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (9055, '导出', 'codegen_template_export', NULL, 9051, NULL, '0', 2, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (9056, '删除', 'codegen_template_del', NULL, 9051, NULL, '0', 3, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (9057, '编辑', 'codegen_template_edit', NULL, 9051, NULL, '0', 4, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (9059, '模板分组', NULL, '/gen/group/index', 9050, 'iconfont icon-shuxingtu', '1', 6, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9060, '查询', 'codegen_group_view', NULL, 9059, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (9061, '新增', 'codegen_group_add', NULL, 9059, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (9062, '修改', 'codegen_group_edit', NULL, 9059, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (9063, '删除', 'codegen_group_del', NULL, 9059, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (9064, '导出', 'codegen_group_export', NULL, 9059, NULL, '0', 0, '0', '0', '1', 'admin', sysdate, ' ', NULL, '0', 1);
INSERT INTO sys_menu VALUES (9065, '字段管理', NULL, '/gen/field-type/index', 9050, 'iconfont icon-fuwenben', '1', 0, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9900, '业务平台', NULL, '/biz', -1, 'iconfont icon-caidan', '1', 2, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9910, '基础工具', NULL, '/tools', -1, 'iconfont icon-gongju', '1', 3, '0', '0', '0', 'admin', sysdate, ' ', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9911, '路由管理', NULL, '/tools/route/index', 9910, 'iconfont icon-crew_feature', '1', 3, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9912, '大屏看板', NULL, '/tools/data/report', 9910, 'iconfont icon-shuju', '1', 5, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);
INSERT INTO sys_menu VALUES (9913, '数据报表', NULL, '/tools/data/jimu', 9910, 'iconfont icon-ico_shuju', '1', 6, '0', '0', '0', 'admin', sysdate, 'admin', sysdate, '0', 1);


INSERT INTO sys_oauth_client_details VALUES (1, 'app', NULL, 'app', 'server', 'password,refresh_token,authorization_code,client_credentials,mobile', 'http://localhost:4040/sso1/login,http://localhost:4041/sso1/login,http://localhost:8080/renren-admin/sys/oauth2-sso,http://localhost:8090/sys/oauth2-sso', NULL, 43200, 2592001, '{"enc_flag":"1","captcha_flag":"1","online_quantity":"1"}', 'true', '0', ' ', 'admin', NULL, sysdate, 1);
INSERT INTO sys_oauth_client_details VALUES (2, 'daemon', NULL, 'daemon', 'server', 'password,refresh_token', NULL, NULL, 43200, 2592001, '{"enc_flag":"1","captcha_flag":"1"}', 'true', '0', ' ', ' ', NULL, NULL, 1);
INSERT INTO sys_oauth_client_details VALUES (3, 'gen', NULL, 'gen', 'server', 'password,refresh_token', NULL, NULL, 43200, 2592001, '{"enc_flag":"1","captcha_flag":"1"}', 'true', '0', ' ', ' ', NULL, NULL, 1);
INSERT INTO sys_oauth_client_details VALUES (4, 'mp', NULL, 'mp', 'server', 'password,refresh_token', NULL, NULL, 43200, 2592001, '{"enc_flag":"1","captcha_flag":"1"}', 'true', '0', ' ', ' ', NULL, NULL, 1);
INSERT INTO sys_oauth_client_details VALUES (5, 'pig', NULL, 'pig', 'server', 'password,refresh_token,authorization_code,client_credentials,mobile', 'http://localhost:4040/sso1/login,http://localhost:4041/sso1/login,http://localhost:8080/renren-admin/sys/oauth2-sso,http://localhost:8090/sys/oauth2-sso', NULL, 43200, 2592001, '{"enc_flag":"1","captcha_flag":"1","online_quantity":"1"}', 'false', '0', ' ', 'admin', NULL, sysdate, 1);
INSERT INTO sys_oauth_client_details VALUES (6, 'test', NULL, 'test', 'server', 'password,refresh_token', NULL, NULL, 43200, 2592001, '{ "enc_flag":"1","captcha_flag":"0"}', 'true', '0', ' ', ' ', NULL, NULL, 1);
INSERT INTO sys_oauth_client_details VALUES (7, 'social', NULL, 'social', 'server', 'password,refresh_token,mobile', NULL, NULL, 43200, 2592001, '{ "enc_flag":"0","captcha_flag":"0"}', 'true', '0', ' ', ' ', NULL, NULL, 1);
INSERT INTO sys_oauth_client_details VALUES (1619615858091982850, 'mini', NULL, 'mini', 'server', 'password,mobile', NULL, NULL, 160000000, 160000000, '{"captcha_flag":"0","enc_flag":"1","online_quantity":"1"}', 'true', '0', 'admin', 'admin', sysdate, sysdate, 1);
INSERT INTO sys_oauth_client_details VALUES (1632018490988806145, 'xzxzccxz', NULL, 'xczcxzxzcxzcxz', 'server', 'password,authorization_code,client_credentials,refresh_token,mobile,implicit', 'https://1237.0.0.1/XX', NULL, 3600, 2592001, '{"captcha_flag":"0","enc_flag":"0","online_quantity":"1"}', 'true', '1', 'admin', 'admin', sysdate, sysdate, 1);
INSERT INTO sys_oauth_client_details VALUES (1635201365770797057, 'sddsa', NULL, 'sadsad', 'serverxxx', 'password,authorization_code,client_credentials,refresh_token', 'sadds', NULL, 43200, 2592001, '{"captcha_flag":"1","enc_flag":"1","online_quantity":"1"}', 'true', '1', 'admin', 'admin', sysdate, sysdate, 1);
INSERT INTO sys_oauth_client_details VALUES (1636620644923621377, 'nov', NULL, 'nov', 'server', 'client_credentials', NULL, NULL, 43200, 2592001, '{"captcha_flag":"0","enc_flag":"1","online_quantity":"1"}', 'true', '0', 'admin', 'admin', sysdate, sysdate, 1);COMMIT;


INSERT INTO sys_post VALUES (1, 'TEAM_LEADER', '部门负责人', 0, 'LEADER', '0', sysdate, ' ', sysdate, 'admin', 1);


INSERT INTO sys_public_param VALUES (1, '租户默认来源', 'TENANT_DEFAULT_ID', '1', '0', '', ' ', ' ', sysdate, sysdate, '2', '0', '1', 1);
INSERT INTO sys_public_param VALUES (2, '租户默认部门名称', 'TENANT_DEFAULT_DEPTNAME', '租户默认部门', '0', '', ' ', ' ', sysdate, NULL, '2', '1', '0', 1);
INSERT INTO sys_public_param VALUES (3, '租户默认账户', 'TENANT_DEFAULT_USERNAME', 'admin', '0', '', ' ', ' ', sysdate, NULL, '2', '1', '0', 1);
INSERT INTO sys_public_param VALUES (4, '租户默认密码', 'TENANT_DEFAULT_PASSWORD', '123456', '0', '', ' ', ' ', sysdate, NULL, '2', '1', '0', 1);
INSERT INTO sys_public_param VALUES (5, '租户默认角色编码', 'TENANT_DEFAULT_ROLECODE', 'ROLE_ADMIN', '0', '', ' ', ' ', sysdate, NULL, '2', '1', '0', 1);
INSERT INTO sys_public_param VALUES (6, '租户默认角色名称', 'TENANT_DEFAULT_ROLENAME', '租户默认角色', '0', '', ' ', ' ', sysdate, NULL, '2', '1', '0', 1);
INSERT INTO sys_public_param VALUES (7, '表前缀', 'GEN_TABLE_PREFIX', 'tb_', '0', '', ' ', ' ', sysdate, NULL, '9', '1', '0', 1);
INSERT INTO sys_public_param VALUES (8, '接口文档不显示的字段', 'GEN_HIDDEN_COLUMNS', 'tenant_id', '0', '', ' ', ' ', sysdate, NULL, '9', '1', '0', 1);
INSERT INTO sys_public_param VALUES (9, '注册用户默认角色', 'USER_DEFAULT_ROLE', 'GENERAL_USER', '0', NULL, ' ', ' ', sysdate, NULL, '2', '1', '0', 1);


INSERT INTO sys_role VALUES (1, '管理员', 'ROLE_ADMIN', '管理员', '0', '', ' ', 'edg134', sysdate, sysdate, '0', 1);
INSERT INTO sys_role VALUES (2, '普通用户', 'GENERAL_USER', '普通用户', '0', '', ' ', 'admin', sysdate, sysdate, '0', 1);


INSERT INTO sys_role_menu VALUES (1, 1000);
INSERT INTO sys_role_menu VALUES (1, 1100);
INSERT INTO sys_role_menu VALUES (1, 1101);
INSERT INTO sys_role_menu VALUES (1, 1102);
INSERT INTO sys_role_menu VALUES (1, 1103);
INSERT INTO sys_role_menu VALUES (1, 1104);
INSERT INTO sys_role_menu VALUES (1, 1200);
INSERT INTO sys_role_menu VALUES (1, 1201);
INSERT INTO sys_role_menu VALUES (1, 1202);
INSERT INTO sys_role_menu VALUES (1, 1203);
INSERT INTO sys_role_menu VALUES (1, 1300);
INSERT INTO sys_role_menu VALUES (1, 1301);
INSERT INTO sys_role_menu VALUES (1, 1302);
INSERT INTO sys_role_menu VALUES (1, 1303);
INSERT INTO sys_role_menu VALUES (1, 1304);
INSERT INTO sys_role_menu VALUES (1, 1305);
INSERT INTO sys_role_menu VALUES (1, 1400);
INSERT INTO sys_role_menu VALUES (1, 1401);
INSERT INTO sys_role_menu VALUES (1, 1402);
INSERT INTO sys_role_menu VALUES (1, 1403);
INSERT INTO sys_role_menu VALUES (1, 1404);
INSERT INTO sys_role_menu VALUES (1, 1500);
INSERT INTO sys_role_menu VALUES (1, 1501);
INSERT INTO sys_role_menu VALUES (1, 1502);
INSERT INTO sys_role_menu VALUES (1, 1503);
INSERT INTO sys_role_menu VALUES (1, 1504);
INSERT INTO sys_role_menu VALUES (1, 1505);
INSERT INTO sys_role_menu VALUES (1, 1506);
INSERT INTO sys_role_menu VALUES (1, 1507);
INSERT INTO sys_role_menu VALUES (1, 1508);
INSERT INTO sys_role_menu VALUES (1, 1600);
INSERT INTO sys_role_menu VALUES (1, 1601);
INSERT INTO sys_role_menu VALUES (1, 1602);
INSERT INTO sys_role_menu VALUES (1, 1603);
INSERT INTO sys_role_menu VALUES (1, 1604);
INSERT INTO sys_role_menu VALUES (1, 1605);
INSERT INTO sys_role_menu VALUES (1, 2000);
INSERT INTO sys_role_menu VALUES (1, 2001);
INSERT INTO sys_role_menu VALUES (1, 2100);
INSERT INTO sys_role_menu VALUES (1, 2101);
INSERT INTO sys_role_menu VALUES (1, 2102);
INSERT INTO sys_role_menu VALUES (1, 2103);
INSERT INTO sys_role_menu VALUES (1, 2104);
INSERT INTO sys_role_menu VALUES (1, 2105);
INSERT INTO sys_role_menu VALUES (1, 2200);
INSERT INTO sys_role_menu VALUES (1, 2201);
INSERT INTO sys_role_menu VALUES (1, 2202);
INSERT INTO sys_role_menu VALUES (1, 2203);
INSERT INTO sys_role_menu VALUES (1, 2210);
INSERT INTO sys_role_menu VALUES (1, 2211);
INSERT INTO sys_role_menu VALUES (1, 2212);
INSERT INTO sys_role_menu VALUES (1, 2213);
INSERT INTO sys_role_menu VALUES (1, 2300);
INSERT INTO sys_role_menu VALUES (1, 2400);
INSERT INTO sys_role_menu VALUES (1, 2401);
INSERT INTO sys_role_menu VALUES (1, 2402);
INSERT INTO sys_role_menu VALUES (1, 2403);
INSERT INTO sys_role_menu VALUES (1, 2500);
INSERT INTO sys_role_menu VALUES (1, 2501);
INSERT INTO sys_role_menu VALUES (1, 2502);
INSERT INTO sys_role_menu VALUES (1, 2503);
INSERT INTO sys_role_menu VALUES (1, 2600);
INSERT INTO sys_role_menu VALUES (1, 2601);
INSERT INTO sys_role_menu VALUES (1, 2800);
INSERT INTO sys_role_menu VALUES (1, 2810);
INSERT INTO sys_role_menu VALUES (1, 2820);
INSERT INTO sys_role_menu VALUES (1, 2830);
INSERT INTO sys_role_menu VALUES (1, 2840);
INSERT INTO sys_role_menu VALUES (1, 2850);
INSERT INTO sys_role_menu VALUES (1, 2860);
INSERT INTO sys_role_menu VALUES (1, 2870);
INSERT INTO sys_role_menu VALUES (1, 2871);
INSERT INTO sys_role_menu VALUES (1, 2900);
INSERT INTO sys_role_menu VALUES (1, 2901);
INSERT INTO sys_role_menu VALUES (1, 2902);
INSERT INTO sys_role_menu VALUES (1, 2903);
INSERT INTO sys_role_menu VALUES (1, 2904);
INSERT INTO sys_role_menu VALUES (1, 2905);
INSERT INTO sys_role_menu VALUES (1, 2906);
INSERT INTO sys_role_menu VALUES (1, 2907);
INSERT INTO sys_role_menu VALUES (1, 3000);
INSERT INTO sys_role_menu VALUES (1, 3001);
INSERT INTO sys_role_menu VALUES (1, 3002);
INSERT INTO sys_role_menu VALUES (1, 3003);
INSERT INTO sys_role_menu VALUES (1, 3004);
INSERT INTO sys_role_menu VALUES (1, 3005);
INSERT INTO sys_role_menu VALUES (1, 3006);
INSERT INTO sys_role_menu VALUES (1, 3007);
INSERT INTO sys_role_menu VALUES (1, 3008);
INSERT INTO sys_role_menu VALUES (1, 3009);
INSERT INTO sys_role_menu VALUES (1, 3010);
INSERT INTO sys_role_menu VALUES (1, 3011);
INSERT INTO sys_role_menu VALUES (1, 3012);
INSERT INTO sys_role_menu VALUES (1, 3013);
INSERT INTO sys_role_menu VALUES (1, 3014);
INSERT INTO sys_role_menu VALUES (1, 3015);
INSERT INTO sys_role_menu VALUES (1, 3016);
INSERT INTO sys_role_menu VALUES (1, 3017);
INSERT INTO sys_role_menu VALUES (1, 3018);
INSERT INTO sys_role_menu VALUES (1, 3019);
INSERT INTO sys_role_menu VALUES (1, 3020);
INSERT INTO sys_role_menu VALUES (1, 3021);
INSERT INTO sys_role_menu VALUES (1, 3022);
INSERT INTO sys_role_menu VALUES (1, 3023);
INSERT INTO sys_role_menu VALUES (1, 3024);
INSERT INTO sys_role_menu VALUES (1, 3025);
INSERT INTO sys_role_menu VALUES (1, 3026);
INSERT INTO sys_role_menu VALUES (1, 3027);
INSERT INTO sys_role_menu VALUES (1, 3028);
INSERT INTO sys_role_menu VALUES (1, 3029);
INSERT INTO sys_role_menu VALUES (1, 4000);
INSERT INTO sys_role_menu VALUES (1, 4001);
INSERT INTO sys_role_menu VALUES (1, 4002);
INSERT INTO sys_role_menu VALUES (1, 4010);
INSERT INTO sys_role_menu VALUES (1, 4011);
INSERT INTO sys_role_menu VALUES (1, 4012);
INSERT INTO sys_role_menu VALUES (1, 4013);
INSERT INTO sys_role_menu VALUES (1, 4014);
INSERT INTO sys_role_menu VALUES (1, 5000);
INSERT INTO sys_role_menu VALUES (1, 5001);
INSERT INTO sys_role_menu VALUES (1, 5002);
INSERT INTO sys_role_menu VALUES (1, 5003);
INSERT INTO sys_role_menu VALUES (1, 5004);
INSERT INTO sys_role_menu VALUES (1, 5005);
INSERT INTO sys_role_menu VALUES (1, 5006);
INSERT INTO sys_role_menu VALUES (1, 5007);
INSERT INTO sys_role_menu VALUES (1, 5008);
INSERT INTO sys_role_menu VALUES (1, 5009);
INSERT INTO sys_role_menu VALUES (1, 5010);
INSERT INTO sys_role_menu VALUES (1, 5011);
INSERT INTO sys_role_menu VALUES (1, 5012);
INSERT INTO sys_role_menu VALUES (1, 5013);
INSERT INTO sys_role_menu VALUES (1, 5014);
INSERT INTO sys_role_menu VALUES (1, 5015);
INSERT INTO sys_role_menu VALUES (1, 5016);
INSERT INTO sys_role_menu VALUES (1, 5017);
INSERT INTO sys_role_menu VALUES (1, 5018);
INSERT INTO sys_role_menu VALUES (1, 5019);
INSERT INTO sys_role_menu VALUES (1, 5020);
INSERT INTO sys_role_menu VALUES (1, 5021);
INSERT INTO sys_role_menu VALUES (1, 5022);
INSERT INTO sys_role_menu VALUES (1, 5023);
INSERT INTO sys_role_menu VALUES (1, 5024);
INSERT INTO sys_role_menu VALUES (1, 5025);
INSERT INTO sys_role_menu VALUES (1, 5026);
INSERT INTO sys_role_menu VALUES (1, 5027);
INSERT INTO sys_role_menu VALUES (1, 5028);
INSERT INTO sys_role_menu VALUES (1, 5029);
INSERT INTO sys_role_menu VALUES (1, 5030);
INSERT INTO sys_role_menu VALUES (1, 5031);
INSERT INTO sys_role_menu VALUES (1, 6000);
INSERT INTO sys_role_menu VALUES (1, 6001);
INSERT INTO sys_role_menu VALUES (1, 6002);
INSERT INTO sys_role_menu VALUES (1, 6003);
INSERT INTO sys_role_menu VALUES (1, 6004);
INSERT INTO sys_role_menu VALUES (1, 6005);
INSERT INTO sys_role_menu VALUES (1, 6006);
INSERT INTO sys_role_menu VALUES (1, 6007);
INSERT INTO sys_role_menu VALUES (1, 6008);
INSERT INTO sys_role_menu VALUES (1, 7000);
INSERT INTO sys_role_menu VALUES (1, 7100);
INSERT INTO sys_role_menu VALUES (1, 7101);
INSERT INTO sys_role_menu VALUES (1, 7102);
INSERT INTO sys_role_menu VALUES (1, 7103);
INSERT INTO sys_role_menu VALUES (1, 7104);
INSERT INTO sys_role_menu VALUES (1, 7200);
INSERT INTO sys_role_menu VALUES (1, 7201);
INSERT INTO sys_role_menu VALUES (1, 7202);
INSERT INTO sys_role_menu VALUES (1, 7203);
INSERT INTO sys_role_menu VALUES (1, 7204);
INSERT INTO sys_role_menu VALUES (1, 7300);
INSERT INTO sys_role_menu VALUES (1, 7301);
INSERT INTO sys_role_menu VALUES (1, 7302);
INSERT INTO sys_role_menu VALUES (1, 7303);
INSERT INTO sys_role_menu VALUES (1, 7400);
INSERT INTO sys_role_menu VALUES (1, 7401);
INSERT INTO sys_role_menu VALUES (1, 7402);
INSERT INTO sys_role_menu VALUES (1, 7403);
INSERT INTO sys_role_menu VALUES (1, 7404);
INSERT INTO sys_role_menu VALUES (1, 7405);
INSERT INTO sys_role_menu VALUES (1, 7500);
INSERT INTO sys_role_menu VALUES (1, 7501);
INSERT INTO sys_role_menu VALUES (1, 7502);
INSERT INTO sys_role_menu VALUES (1, 7503);
INSERT INTO sys_role_menu VALUES (1, 7504);
INSERT INTO sys_role_menu VALUES (1, 7505);
INSERT INTO sys_role_menu VALUES (1, 7600);
INSERT INTO sys_role_menu VALUES (1, 7700);
INSERT INTO sys_role_menu VALUES (1, 7701);
INSERT INTO sys_role_menu VALUES (1, 9000);
INSERT INTO sys_role_menu VALUES (1, 9005);
INSERT INTO sys_role_menu VALUES (1, 9006);
INSERT INTO sys_role_menu VALUES (1, 9007);
INSERT INTO sys_role_menu VALUES (1, 9050);
INSERT INTO sys_role_menu VALUES (1, 9051);
INSERT INTO sys_role_menu VALUES (1, 9052);
INSERT INTO sys_role_menu VALUES (1, 9053);
INSERT INTO sys_role_menu VALUES (1, 9054);
INSERT INTO sys_role_menu VALUES (1, 9055);
INSERT INTO sys_role_menu VALUES (1, 9056);
INSERT INTO sys_role_menu VALUES (1, 9057);
INSERT INTO sys_role_menu VALUES (1, 9059);
INSERT INTO sys_role_menu VALUES (1, 9060);
INSERT INTO sys_role_menu VALUES (1, 9061);
INSERT INTO sys_role_menu VALUES (1, 9062);
INSERT INTO sys_role_menu VALUES (1, 9063);
INSERT INTO sys_role_menu VALUES (1, 9064);
INSERT INTO sys_role_menu VALUES (1, 9065);
INSERT INTO sys_role_menu VALUES (1, 9900);
INSERT INTO sys_role_menu VALUES (1, 9910);
INSERT INTO sys_role_menu VALUES (1, 9911);
INSERT INTO sys_role_menu VALUES (1, 9912);
INSERT INTO sys_role_menu VALUES (1, 9913);


INSERT INTO sys_route_conf VALUES (1, '工作流管理模块', 'pigx-oa-platform', '[{\"args\": {\"_genkey_0\": \"/act/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-oa-platform', 0, NULL, ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (2, '认证中心', 'pigx-auth', '[{\"args\": {\"_genkey_0\": \"/auth/**\"}, \"name\": \"Path\"}]', '[{\"args\": {}, \"name\": \"ValidateCodeGatewayFilter\"}, {\"args\": {}, \"name\": \"PasswordDecoderFilter\"}]', 'lb://pigx-auth', 0, NULL, ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (3, '代码生成模块', 'pigx-codegen', '[{\"args\": {\"_genkey_0\": \"/gen/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-codegen', 0, NULL, ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (4, 'elastic-job定时任务模块', 'pigx-daemon-elastic-job', '[{\"args\": {\"_genkey_0\": \"/daemon/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-daemon-elastic-job', 0, NULL, ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (5, 'quartz定时任务模块', 'pigx-daemon-quartz', '[{\"args\": {\"_genkey_0\": \"/job/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-daemon-quartz', 0, NULL, ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (6, '分布式事务模块', 'pigx-tx-manager', '[{\"args\": {\"_genkey_0\": \"/tx/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-tx-manager', 0, NULL, ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (7, '通用权限模块', 'pigx-upms-biz', '[{\"args\": {\"_genkey_0\": \"/admin/**\"}, \"name\": \"Path\"}]', '[{\"args\": {\"key-resolver\": \"#{@remoteAddrKeyResolver}\", \"redis-rate-limiter.burstCapacity\": \"1000\", \"redis-rate-limiter.replenishRate\": \"1000\"}, \"name\": \"RequestRateLimiter\"}]', 'lb://pigx-upms-biz', 0, '{\"response-timeout\": \"30000\"}', ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (8, '工作流长链接支持', 'pigx-oa-platform-ws', '[{\"args\": {\"_genkey_0\": \"/act/ws/**\"}, \"name\": \"Path\"}]', '[]', 'lb:ws://pigx-oa-platform', 100, NULL, ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (9, '微信公众号管理', 'pigx-mp-platform', '[{\"args\": {\"_genkey_0\": \"/mp/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-mp-platform', 0, NULL, ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (10, '支付管理', 'pigx-pay-platform', '[{\"args\": {\"_genkey_0\": \"/pay/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-pay-platform', 0, NULL, ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (11, '监控管理', 'pigx-monitor', '[{\"args\": {\"_genkey_0\": \"/monitor/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-monitor', 0, NULL, ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (12, '积木报表', 'pigx-jimu-platform\n', '[{\"args\": {\"_genkey_0\": \"/jimu/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-jimu-platform', 0, NULL, ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (13, '大屏设计', 'pigx-report-platform', '[{\"args\": {\"_genkey_0\": \"/gv/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-report-platform', 0, '{}', ' ', ' ', sysdate, sysdate, '0');
INSERT INTO sys_route_conf VALUES (14, 'APP服务', 'pigx-app-server', '[{\"args\": {\"_genkey_0\": \"/app/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-app-server-biz', 0, '{}', 'admin', ' ', sysdate, NULL, '0');
INSERT INTO sys_route_conf VALUES (15, '工作流引擎', 'pigx-flow-task-biz', '[{\"args\": {\"_genkey_0\": \"/task/**\"}, \"name\": \"Path\"}]', '[]', 'lb://pigx-flow-task-biz', 0, '{}', ' ', ' ', sysdate, NULL, '0');


INSERT INTO sys_tenant VALUES (1, '北京分公司', '1', '', NULL, NULL, NULL, NULL, NULL, sysdate, sysdate, '0', '0', ' ', 'admin', sysdate, sysdate, 1642752536722997250);

INSERT INTO sys_user VALUES (1, 'admin', '$2a$10$c/Ae0pRjJtMZg3BnvVpO.eIK6WYWVbKTzqgdy3afR7w.vd.xi3Mgy', '', '13054729089', '/admin/sys-file/local/2a14ae08150e483c93e12ac8934173e2.png', '管理员666777', '管理员', '<EMAIL>', 4, ' ', 'admin', sysdate, sysdate, '0', '0', NULL, 'oBxPy5E-v82xWGsfzZVzkD3wEX64', NULL, 'log4j', NULL, NULL,NULL,1);

INSERT INTO sys_user_post VALUES (1, 1);

INSERT INTO sys_user_role VALUES (1, 1);

