alter table SYS_USER add id nvarchar2(64);
comment on column SYS_USER.id is 'userId业务系统使用';


ALTER TABLE SYS_USER_ROLE ADD ORG_ID NVARCHAR2(64);
COMMENT ON COLUMN SYS_USER_ROLE.ORG_ID IS '租户ID';

INSERT INTO "SYS_USER" (
    "ID",
    "USERNA<PERSON>",
    "PASSWORD",
    "NAME",
    "<PERSON><PERSON>H",
    "<PERSON><PERSON><PERSON><PERSON>",
    "ORG_ID",
    "STATUS",
    "CREATE_TIME",
    "UPDATE_TIME",
    "AVATAR",
    "IS_CRP_USER",
    "EMPLOYEE_NUM",
    "USER_ID",
    "SALT",
    "N<PERSON><PERSON><PERSON><PERSON>",
    "EMAIL",
    "DEPT_ID",
    "CREATE_BY",
    "UPDATE_BY",
    "DEL_FLAG",
    "WX_OPENID",
    "MINI_OPENID",
    "QQ_OPENID",
    "GITEE_LOGIN",
    "OSC_ID",
    "WX_CP_USERID",
    "WX_DING_USERID",
    "TENANT_ID",
    "LOCK_FLAG"
)
SELECT
    "ID",
    "USERNAME",
    '$2a$10$c/Ae0pRjJtMZg3BnvVpO.eIK6WYWVbKTzqgdy3afR7w.vd.xi3Mgy' as "PASSWORD",
    "NAME",
    "SFZH",
    "MOBILE",
    nvl("ORG_ID", '1') as "ORG_ID",
    "STATUS",
    "CREATE_TIME",
    "UPDATE_TIME",
    "AVATAR",
    "IS_CRP_USER",
    "EMPLOYEE_NUM",
    ROW_NUMBER() OVER (ORDER BY "ID") AS "USER_ID",
    NULL AS "SALT",
    NULL AS "NICKNAME",
    NULL AS "EMAIL",
    NULL AS "DEPT_ID",
    '1' AS "CREATE_BY",
    '1' AS "UPDATE_BY",
    '0' AS "DEL_FLAG",
    NULL AS "WX_OPENID",
    NULL AS "MINI_OPENID",
    NULL AS "QQ_OPENID",
    NULL AS "GITEE_LOGIN",
    NULL AS "OSC_ID",
    NULL AS "WX_CP_USERID",
    NULL AS "WX_DING_USERID",
    nvl("ORG_ID", '1') AS "TENANT_ID",
    0 AS "LOCK_FLAG"
FROM "SYS_USER_OLD";


INSERT INTO SYS_USER_ROLE("USER_ID", "ROLE_ID") SELECT
                                                    sys_user.USER_ID as "USER_ID",
                                                    sys_role.role_id as "ROLE_ID"
FROM
    SYS_USER_2_ROLE
        INNER JOIN sys_role ON sys_role.role_code = SYS_USER_2_ROLE.role
        inner join sys_user on sys_user.id = SYS_USER_2_ROLE.USER_ID