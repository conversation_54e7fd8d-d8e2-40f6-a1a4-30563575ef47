<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xunw</groupId>
        <artifactId>training-parent</artifactId>
        <version>5.3.0</version>
    </parent>
    <artifactId>training-auth</artifactId>
    <packaging>jar</packaging>
    <description>认证授权中心，基于 spring security oAuth2</description>

    <dependencies>
        <!--log-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-log</artifactId>
        </dependency>
        <!--security-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-security</artifactId>
        </dependency>
        <!--缓存操作-->
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>common-data</artifactId>
        </dependency>
       	 <!--滑块验证码-->
        <dependency>
            <groupId>com.github.anji-plus</groupId>
            <artifactId>captcha-spring-boot-starter</artifactId>
            <version>${captcha.version}</version>
        </dependency>
        <!--图形验证码-->
        <dependency>
            <groupId>io.springboot.plugin</groupId>
            <artifactId>captcha-core</artifactId>
            <version>${captcha.image.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>
    </dependencies>
</project>
