package com.xunw.cloud.auth.endpoint;

import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletResponse;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.xunw.cloud.common.core.constant.CacheConstants;
import com.xunw.cloud.common.core.constant.CommonConstants;
import com.xunw.cloud.common.core.constant.SecurityConstants;
import com.xunw.cloud.common.core.util.R;
import com.xunw.cloud.common.core.util.SpringContextHolder;
import com.xunw.cloud.common.security.annotation.Inner;

import io.springboot.captcha.ArithmeticCaptcha;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;

/**
 * 验证码相关的接口
 *
 * <AUTHOR>
 * @date 2022/6/27
 */
@Inner(false)
@RestController
@RequestMapping("/code")
@RequiredArgsConstructor
public class ImageCodeController {

	private static final Integer DEFAULT_IMAGE_WIDTH = 100;

	private static final Integer DEFAULT_IMAGE_HEIGHT = 40;

	private final StringRedisTemplate redisTemplate;

	/**
	 * 创建图形验证码
	 */
	@SneakyThrows
	@GetMapping("/image")
	public void image(String randomStr, HttpServletResponse response) {
		ArithmeticCaptcha captcha = new ArithmeticCaptcha(DEFAULT_IMAGE_WIDTH, DEFAULT_IMAGE_HEIGHT);

		String result = captcha.text();
		redisTemplate.opsForValue()
			.set(CacheConstants.DEFAULT_CODE_KEY + randomStr, result, SecurityConstants.CODE_TIME, TimeUnit.SECONDS);
		// 转换流信息写出
		captcha.out(response.getOutputStream());
	}

	/**
	 * 创建滑块验证码
	 * @return R
	 */
	@GetMapping("/create")
	public R create() {
		CaptchaVO vo = new CaptchaVO();
		vo.setCaptchaType(CommonConstants.IMAGE_CODE_TYPE);
		CaptchaService captchaService = SpringContextHolder.getBean(CaptchaService.class);
		ResponseModel responseModel = captchaService.get(vo);
		return R.ok(responseModel);
	}

	/**
	 * 校验验证码
	 * @param pointJson 位置坐标
	 * @param token 随机串
	 * @return R
	 */
	@PostMapping("/check")
	public R check(String pointJson, String token) {
		CaptchaVO vo = new CaptchaVO();
		vo.setPointJson(pointJson);
		vo.setToken(token);
		vo.setCaptchaType(CommonConstants.IMAGE_CODE_TYPE);
		CaptchaService captchaService = SpringContextHolder.getBean(CaptchaService.class);
		ResponseModel responseModel = captchaService.check(vo);
		return R.ok(responseModel);
	}

}
