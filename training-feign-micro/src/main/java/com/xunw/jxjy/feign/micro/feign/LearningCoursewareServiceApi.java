package com.xunw.jxjy.feign.micro.feign;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xunw.jxjy.feign.micro.config.FeignConfig;
import com.xunw.jxjy.feign.micro.dto.courseware.FeignCoursewareCommentDTO;
import com.xunw.jxjy.feign.micro.dto.courseware.FeignCoursewareDTO;
import com.xunw.jxjy.feign.micro.dto.courseware.FeignCoursewareNoteDTO;
import com.xunw.jxjy.feign.micro.dto.courseware.FeignCoursewareRecordDTO;
import com.xunw.jxjy.feign.micro.vo.CoursewareVo;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "remoteLearningCoursewareServiceApiService", name = "learning-courseware-service", configuration = FeignConfig.class)
public interface LearningCoursewareServiceApi {

    @RequestMapping(value = "/api/courseware/list", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Page<FeignCoursewareDTO> coursewareList(@RequestParam(required = false, value = "source") Object source,
                                            @RequestParam(required = false, value = "courseId") Object courseId,
                                            @RequestParam(required = false, value = "status") Object status,
                                            @RequestParam(required = false, value = "isTest") Object isTest,
                                            @RequestParam(required = false, value = "orgIds") Object orgIds,
                                            @RequestParam(required = false, value = "idNot") Object idNot,
                                            @RequestParam(required = false, value = "idsIn") Object idsIn,
                                            @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                                            @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/courseware/getById")
    @Headers({"Content-Type=multipart/form-data"})
    FeignCoursewareDTO coursewareGetById(@RequestParam(required = false, value = "id") Object id,
                                         @RequestParam(required = false, value = "courseId") Object courseId);

    @RequestMapping(value = "api/courseware/add", method = RequestMethod.POST, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String coursewareAdd(@RequestBody CoursewareVo coursewareVo);

    @RequestMapping(value = "api/courseware/edit", method = RequestMethod.POST, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Boolean coursewareEdit(@RequestBody CoursewareVo coursewareVo);

    @RequestMapping("api/courseware/editStatus")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean coursewareEditStatus(@RequestParam(required = false,value = "id") Object id,
                                   @RequestParam(required = false,value = "status") Object status);

    @RequestMapping("api/courseware/remove")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean coursewareRemove(@RequestParam(required = false,value = "id") String id);

    @RequestMapping("api/study/addLearn")
    @Headers({"Content-Type=multipart/form-data"})
    FeignCoursewareRecordDTO studyAddLearn(@RequestParam(required = false,value = "studentId") Object studentId,
                                           @RequestParam(required = false,value = "batchId") Object batchId,
                                           @RequestParam(required = false,value = "coursewareId") Object coursewareId,
                                           @RequestParam(required = false,value = "chapterId") Object chapterId,
                                           @RequestParam(required = false,value = "lessonId") Object lessonId);

    @RequestMapping("/api/study/getLearn")
    @Headers({"Content-Type=multipart/form-data"})
    FeignCoursewareDTO studyGetLearn(@RequestParam(required = false, value = "studentId") Object studentId,
                                     @RequestParam(required = false, value = "batchId") Object batchId,
                                     @RequestParam(required = false, value = "coursewareId") Object coursewareId);


    /** 添加学习笔记
     * @param lessonId
     * @param pointTime
     * @param content
     * @return
     */
    @RequestMapping("/api/study/addLearnNote")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean addLearnNote(@RequestParam(required = false, value = "lessonId")String lessonId,
                           @RequestParam(required = false, value = "pointTime")Long pointTime,
                           @RequestParam(required = false, value = "content")String content);

    /** 删除学习笔记
     * @param id
     * @return
     */
    @RequestMapping("/api/study/deleteLearnNote")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean deleteLearnNotes(@RequestParam(required = false, value = "id")String id);

    /** 获取学习笔记
     * @param lessonId
     * @return
     */
    @RequestMapping("/api/study/getLearnNotes")
    @Headers({"Content-Type=multipart/form-data"})
    List<FeignCoursewareNoteDTO> getLearnNotes(@RequestParam(required = false, value = "lessonId")String lessonId);

    /**课件添加评论
     * @param lessonId
     * @param content
     * @return
     */
    @RequestMapping("/api/study/addLearnComment")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean addLearnComment(@RequestParam(required = false, value = "lessonId")String lessonId,
                              @RequestParam(required = false, value = "content")String content);

    /**删除课件评论
     * @param id
     * @return
     */
    @RequestMapping("/api/study/deleteLearnComment")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean deleteLearnComment(@RequestParam(required = false, value = "id")String id);


    /** 获取课件评论
     * @param lessonId
     * @return
     */
    @RequestMapping("/api/study/getLearnComments")
    @Headers({"Content-Type=multipart/form-data"})
    List<FeignCoursewareCommentDTO> getLearnComments(@RequestParam(required = false, value = "lessonId")String lessonId);

    /** 保存课件评分
     * @param coursewareId
     * @param star
     * @return
     */
    @RequestMapping("/api/study/addLearnStar")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean addLearnStar(@RequestParam(required = false, value = "coursewareId") String coursewareId,
                           @RequestParam(required = false, value = "star") Integer star);

    @RequestMapping("/api/courseware/doAssign")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean batchAuthorize(@RequestParam(required = false, value = "coursewareId")String coursewareId,
                             @RequestParam(required = false, value = "orgId")String orgId);

    @RequestMapping("/api/courseware/getAssign")
    @Headers({"Content-Type=multipart/form-data"})
    List<Long> getAssign(@RequestParam(required = false, value = "coursewareId")String coursewareId);


    @RequestMapping("/api/courseware/deleteAssign")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean deleteAssign(@RequestParam(required = false, value = "coursewareId")String coursewareId,
                           @RequestParam(required = false, value = "orgId")String orgId);
}
