package com.xunw.jxjy.feign.micro.dto.exam;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * @TableName es_b_old_data
 */
@Data
public class FeignOldDataDTO implements Serializable {

	private static final long serialVersionUID = 1058016161048221072L;

	private String id;

	/**
	 * 耗时（秒）
	 */
	private Long duration;

	/**
	 * 考试开始时间
	 */
	private String startTime;

	/**
	 * 考试结束时间
	 */
	private String endTime;

	/**
	 * 试卷id
	 */
	private String paperId;

	/**
	 * 状态，NOTSTART-未开始、STARTED-进行中、SUBMITTED-已提交、REVISED-已批改、CLOSED-已关闭
	 */
	private String status;

	/**
	 * 批改人
	 */
	private String checkUserId;

	/**
	 * 作答用户id
	 */
	private String studentId;

	/**
	 * 得分
	 */
	private Integer score;

	/**
	 * 机构id
	 */
	private String orgId;

	/**
	 * 系统标识
	 */
	private String appId;

	/**
	 * 创建人
	 */
	private String createdBy;

	/**
	 * 创建时间
	 */
	private String createdTime;

	/**
	 * 最后修改人
	 */
	private String lastModifiedBy;

	/**
	 * 最后修改时间
	 */
	private String lastModifiedTime;

	/**
	 * 总分
	 */
	private Integer totalScore;

	/**
	 * 及格分
	 */
	private Integer passScore;



	/**
	 * 试卷名称
	 */
	private String name;

	/**
	 * 年份
	 */
	private Integer year;

	/**
	 * 月份
	 */
	private Integer month;

	/**
	 * 省份
	 */
	private String province;

	private String batchId;

	private BigDecimal originalPrice;

	private BigDecimal discountPrice;

	private Integer purchaseCount;

	private Integer isBoutique;

	private Integer ownership;

	private String imgUrl;

	private String courseCode;

	private String courseName;

	private Long schoolId;

	private String schoolCode;


	private String schoolName;


	private Long regGroupId;
	private String regGroupCode;


	private String regGroupName;

	private String groupName;
	private String groupPointName;


	private String zkzNo;

	private String certino;

	private String studentName;
	
	private String phone;

}
