package com.xunw.jxjy.feign.micro.dto.courseware;

import lombok.Data;

import java.io.Serializable;

/**
 * 记录考生课件学习的学习记录、学习时长
 * @TableName ls_b_courseware_record
 */
@Data
public class FeignCoursewareRecordDTO implements Serializable {

    private static final long serialVersionUID = -2040665905436398484L;

    /**
     * 主键
     */
    private String id;

    /**
     * 学习批次
     */
    private String batchId;

    /**
     * 学生id
     */
    private String studentId;

    /**
     * 课件id
     */
    private String coursewareId;

    /**
     * 章节id
     */
    private String chapterId;

    /**
     * 课时id
     */
    private String lessonId;

    /**
     * 学习开始时间
     */
    private String startTime;

    /**
     * 学习结束时间
     */
    private String endTime;

    /**
     * 视频总时长
     */
    private Integer totalDuration;

    /**
     * 累计观看时长（单位：分钟）
     */
    private Integer duration;

    /**
     * 是否已学完;(1 是； 0 否)
     */
    private Integer isFinished;

    /**
     * 更新时间
     */
    private String modifyTime;

    /**
     * 系统标识
     */
    private String appId;

    private String orgId;

}
