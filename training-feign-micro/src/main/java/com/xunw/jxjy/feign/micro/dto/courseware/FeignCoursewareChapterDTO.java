package com.xunw.jxjy.feign.micro.dto.courseware;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 课件章节表
 * @TableName ls_b_courseware_chapter
 */
@Data
public class FeignCoursewareChapterDTO implements Serializable {
    private static final long serialVersionUID = 4067504365865372617L;

    /**
     * id
     */
    private String id;

    /**
     * 课件id
     */
    private String coursewareId;

    /**
     * 章节序号
     */
    private Integer sortNumber;

    /**
     * 章节名称
     */
    private String name;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private String modifyTime;

    /**
     * 修改人
     */
    private String modifyor;

    /**
     * 课件课时
     */
    private List<FeignCoursewareLessonDTO> lessons;

}
