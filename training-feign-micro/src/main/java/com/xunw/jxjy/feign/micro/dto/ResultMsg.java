package com.xunw.jxjy.feign.micro.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import org.apache.poi.ss.formula.functions.T;

import java.io.Serializable;

@JsonInclude()
public class ResultMsg<T> implements Serializable {

	private static final long serialVersionUID = -4466510820496221280L;

	private int code;

    private T data;

	@JsonInclude(value = Include.NON_NULL)
    private String message;

    public void returnData(T data) {
        this.code = 0;
        this.data = data;
    }

    public void returnError(int code, String message) {
        this.code = code;
        this.message = message;
    }

	public Boolean isSuccess() {
		if (this.code != 0) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

	/**
	 * @return the code
	 */
	public int getCode() {
		return code;
	}

	/**
	 * @param code the code to set
	 */
	public void setCode(int code) {
		this.code = code;
	}

	/**
	 * @return the data
	 */
	public T getData() {
		return data;
	}

	/**
	 * @param data the data to set
	 */
	public void setData(T data) {
		this.data = data;
	}

	/**
	 * @return the message
	 */
	public String getMessage() {
		return message;
	}

	/**
	 * @param message the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}
}
