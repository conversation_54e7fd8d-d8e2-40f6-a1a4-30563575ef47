package com.xunw.jxjy.feign.micro.config;

import com.google.gson.Gson;
import com.xunw.cloud.common.core.exception.BizException;
import com.xunw.jxjy.feign.micro.dto.ResultMsg;
import feign.FeignException;
import feign.Response;
import feign.codec.Decoder;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.cloud.openfeign.support.SpringDecoder;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * @version 1.0
 * @Description
 * <AUTHOR>
 * @date 2023/11/24 16:29
 */
public class OrgFeignDecoder implements Decoder {

    private Gson gson = new Gson();

    private final SpringDecoder decoder;

    public OrgFeignDecoder(SpringDecoder decoder) {
        this.decoder = decoder;
    }

    @Override
    public Object decode(Response response, Type type) throws IOException, FeignException {
        FeignParameterizedType respType = new FeignParameterizedType(type);

        ResultMsg<T> baseResponse = gson.fromJson(response.body().asReader(), respType);

        if (type instanceof ResultMsg) {
            return baseResponse;
        }
        if (baseResponse.isSuccess()) {
            return baseResponse.getData();
        }
        throw BizException.withMessage(baseResponse.getCode(),baseResponse.getMessage());
    }

}
