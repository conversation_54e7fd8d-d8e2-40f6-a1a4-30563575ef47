package com.xunw.jxjy.feign.micro.dto.exam;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * @TableName es_b_question_db
 */
@Data
public class FeignQuestionDbDTO implements Serializable {

    private static final long serialVersionUID = -8211471206874267376L;

    /**
     * id
     */
    private String id;

    /**
     * 题库名
     */
    private String name;

    /**
     * 状态
     */
    private String status;

    /**
     * 课程id
     */
    private String courseId;

    /**
     * 是否真题
     */
    private String isOldPaper;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 题库图标url
     */
    private String icon;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createdTime;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    private String lastModifiedTime;

    /**
     * 系统标识
     */
    private String appId;

    private Integer tag;//标签 1随堂练习 2模拟练习


    private Integer questionCount;//题目数量

    private Integer isPractice;//是否练习过0否1是


    /**
     * 业务需要字段  start -------------------------------------
     */
    private String courseCode;

    private String courseName;

    private String orgCode;

    private String orgName;

    /**
     * 业务需要字段  end -------------------------------------
     */
}
