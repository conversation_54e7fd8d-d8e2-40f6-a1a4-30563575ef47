package com.xunw.jxjy.feign.micro.dto.courseware;

import lombok.Data;

import java.io.Serializable;

/**
 * 课件课时表
 * @TableName ls_b_courseware_lesson
 */
@Data
public class FeignCoursewareLessonDTO implements Serializable {

    private static final long serialVersionUID = 1909196568321432861L;

    /**
     * id
     */
    private String id;

    /**
     * 章节id
     */
    private String chapterId;

    /**
     * 课件序号
     */
    private Integer sortNumber;

    /**
     * 课时名称
     */
    private String name;

    /**
     * 课时介绍
     */
    private String remark;

    /**
     * 课件资源类型
     */
    private String fileType;

    /**
     * 课件时长
     */
    private int minutes;

    /**
     * 课件地址
     */
    private String filePath;

    /**
     * 弹题间隔时间(分钟)
     */
    private int timint;

    /**
     * 题库ids,以英文逗号分割
     */
    private String quesDbIds;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private String modifyTime;

    /**
     * 修改人
     */
    private String modifyor;

    /**
     * 观看时长
     */
    private int duration;

    /**
     * 是否看完
     */
    private int isFinished;

}
