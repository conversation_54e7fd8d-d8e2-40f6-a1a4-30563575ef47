package com.xunw.jxjy.feign.micro.dto.live;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ls_b_live_lesson
 * <AUTHOR>
@Data
public class FeignLiveLessonDTO implements Serializable {

    private static final long serialVersionUID = -5698711264946863616L;

    /**
     * id
     */
    private String id;

    /**
     * 课件章节id
     */
    private String chapterId;

    /**
     * 课时序号
     */
    private Integer sortNumber;

    /**
     * 直播标题
     */
    private String name;

    /**
     * 直播讲师
     */
    private String teacherId;

    /**
     * 课时介绍
     */
    private String remark;

    /**
     * 直播地点
     */
    private String address;

    /**
     * 累计观看人数
     */
    private Integer viewNum;

    /**
     * 最高观看人数
     */
    private Integer maxNum;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 直播状态：未开始（READY）、正在直播（PLAYING）、已结束（FINISHED）、已下线（OFFLINE）
     */
    private String status;

    /**
     * 直播流状态
     */
    private String streamStatus;

    private String teacherStreamStatus;//教师流状态

    /**
     * 直播流名称-视频流
     */
    private String videoName;

    /**
     * 直播流名称-教师流
     */
    private String teaName;

    /**
     * 视频流-推流地址
     */
    private String videoPushUrl;

    /**
     * 老师流-推流地址
     */
    private String teaPushUrl;

    /**
     * 推流地址过期时间
     */
    private String expDate;

    /**
     * 视频流播放地址RTMP地址
     */
    private String videoRtmpUrl;

    /**
     * 视频流播放地址FLV地址
     */
    private String videoFlvUrl;

    /**
     * 视频流播放地址HLS地址
     */
    private String videoHlsUrl;

    /**
     * 教师流播放地址RTMP地址
     */
    private String teaRtmpUrl;

    /**
     * 教师流播放地址FLV地址
     */
    private String teaFlvUrl;

    /**
     * 教师流播放地址HLS地址
     */
    private String teaHlsUrl;

    /**
     * 视频流回看地址
     */
    private String videoBackUrl;

    /**
     * 视频流生成回看url的七牛服务器的处理会话ID，如果不为空，表示回放文件还未生成好
     */
    private String videoBackSessionId;

    /**
     * 教师流回看地址
     */
    private String teaBackUrl;

    /**
     * 教师流生成回看url的七牛服务器的处理会话ID，如果不为空，表示回放文件还未生成好
     */
    private String teaBackSessionId;

    /**
     * 回看视频时长(单位秒)
     */
    private Integer backTimes;

    /**
     * 是否全局禁言 (1 是； 0 否)
     */
    private Integer isAllBanpost;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private String modifyTime;

    /**
     * 修改人
     */
    private String modifyor;

    private String star;//直播课评分

    /**
     * 禁言用户ids, 用英文逗号分隔
     */
    private String banpostTargerUserid;

    private List<FeignLiveRecordDTO> watchRecordList;

    List<FeignLiveLearningMaterialsDTO> liveLearningMaterials;//学习资料集合


    private int liveTimes;

    private String teacherName;

    private String teacherUserName;

}
