package com.xunw.jxjy.feign.micro.config;

import com.xunw.jxjy.feign.micro.dto.ResultMsg;
import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;

import java.lang.reflect.MalformedParameterizedTypeException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * @version 1.0
 * @Description
 * <AUTHOR>
 * @date 2024/12/19 9:58
 */
public class FeignParameterizedType implements ParameterizedType {

    private Type type;

    /**
     * 将Feign Client方法的返回值注入，只要两种类型，一种是ParameterizedTypeImpl，另一种是具体的Class对象
     */
    public FeignParameterizedType(Type type) {
        this.type = type;
    }

    /**
     * Returns an array of {@code Type} objects representing the actual type
     * arguments to this type.
     *
     * <p>Note that in some cases, the returned array be empty. This can occur
     * if this type represents a non-parameterized type nested within
     * a parameterized type.
     *
     * @return an array of {@code Type} objects representing the actual type
     * arguments to this type
     * @throws TypeNotPresentException             if any of the
     *                                             actual type arguments refers to a non-existent type declaration
     * @throws MalformedParameterizedTypeException if any of the
     *                                             actual type parameters refer to a parameterized type that cannot
     *                                             be instantiated for any reason
     * @since 1.5
     */
    @Override
    public Type[] getActualTypeArguments() {
        Type[] types = new Type[1];
        types[0] = type;
        return types;
    }

    /**
     * Returns the {@code Type} object representing the class or interface
     * that declared this type.
     *
     * @return the {@code Type} object representing the class or interface
     * that declared this type
     * @since 1.5
     */
    @Override
    public Type getRawType() {
        return ResultMsg.class;
    }

    /**
     * Returns a {@code Type} object representing the type that this type
     * is a member of.  For example, if this type is {@code O<T>.I<S>},
     * return a representation of {@code O<T>}.
     *
     * <p>If this type is a top-level type, {@code null} is returned.
     *
     * @return a {@code Type} object representing the type that
     * this type is a member of. If this type is a top-level type,
     * {@code null} is returned
     * @throws TypeNotPresentException             if the owner type
     *                                             refers to a non-existent type declaration
     * @throws MalformedParameterizedTypeException if the owner type
     *                                             refers to a parameterized type that cannot be instantiated
     *                                             for any reason
     * @since 1.5
     */
    @Override
    public Type getOwnerType() {
        if (type instanceof ParameterizedTypeImpl) {
            ParameterizedTypeImpl typeImpl = (ParameterizedTypeImpl) type;
            return typeImpl.getRawType().getEnclosingClass();
        }

        if (type instanceof Class) {
            return ((Class) type).getEnclosingClass();
        }
        return null;
    }
}
