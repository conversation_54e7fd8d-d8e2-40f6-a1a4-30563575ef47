package com.xunw.jxjy.feign.micro.dto.live;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * ls_b_courselive
 * <AUTHOR>
@Data
public class FeignCourseLiveDTO implements Serializable {
    private static final long serialVersionUID = 2849282665745505212L;
    /**
     * id
     */
    private String id;

    /**
     * 直播课名称
     */
    private String name;

    /**
     * 课程id
     */
    private String courseId;

    /**
     * 直播计划开始时间
     */
    private String startTime;

    /**
     * 直播计划结束时间
     */
    private String endTime;

    /**
     * logo
     */
    private String logo;

    /**
     * 系统标识
     */
    private String appId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private String modifyTime;

    /**
     * 修改人
     */
    private String modifyor;

    /**
     * 直播简介
     */
    private String remark;

    private String orgId;

    private List<FeignLiveChapterDTO> chapters;


    private String courseCode;

    private String courseName;

    private String createName;

    private BigDecimal originalPrice;

    private BigDecimal discountPrice;

    private Integer purchaseCount;

    private Integer isBoutique;

    private Integer ownership;

    private String isPay;
}
