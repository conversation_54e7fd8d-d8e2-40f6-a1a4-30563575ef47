package com.xunw.jxjy.feign.micro.dto.exam;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @TableName es_b_old_paper
 */
@Data
public class FeignOldPaperDTO implements Serializable {

	private static final long serialVersionUID = -2617359075971175581L;

	/**
	 * 考试试卷id
	 */
	private String id;

	/**
	 * 试卷名称
	 */
	private String name;

	/**
	 * 年份
	 */
	private Integer year;

	/**
	 * 月份
	 */
	private Integer month;

	/**
	 * 省份
	 */
	private String province;

	/**
	 * 总分
	 */
	private Integer totalScore;

	/**
	 * 及格分
	 */
	private Integer passScore;


	/**
	 * 允许考试次数
	 */
	private Integer frequency;
	/**
	 * 说明
	 */
	private String remark;

	/**
	 * 试卷题型(主观、客观、主客观)
	 */
	private String paperType;

	/**
	 * 试卷状态: DRAFT("草稿"), DISABLED("禁用"), ENABLE("启用");
	 * 考生查询就是作答状态
	 */
	private String status;

	/**
	 * 系统标识
	 */
	private String appId;

	/**
	 * 课程id
	 */
	private String courseId;

	/**
	 * 创建人
	 */
	private String createdBy;

	/**
	 * 创建时间
	 */
	private String createdTime;

	/**
	 * 最后修改人
	 */
	private String lastModifiedBy;

	/**
	 * 最后修改时间
	 * 考生查询 是 最后作答时间
	 */
	private String lastModifiedTime;


	private List<FeignPaperSectionDTO> sections = new ArrayList<>();

	private List<FeignPaperQuestionDTO> questions = new ArrayList<>();

	private String examDataId;

	private Integer practiceNum;

	private JSONObject answers;

	private JSONObject checks;

	private String batchId;

	private BigDecimal originalPrice;

	private BigDecimal discountPrice;

	private Integer purchaseCount;

	private Integer isBoutique;

	private Integer ownership;

	private String imgUrl;

	private String courseCode;

	private String courseName;

	private String orgId;

	private Long schoolId;

	private String schoolCode;


	private String schoolName;


	private Long regGroupId;
	private String regGroupCode;


	private String regGroupName;

	private String groupName;
	private String groupPointName;


	private String zkzNo;

	private String certino;

	private String studentName;

}
