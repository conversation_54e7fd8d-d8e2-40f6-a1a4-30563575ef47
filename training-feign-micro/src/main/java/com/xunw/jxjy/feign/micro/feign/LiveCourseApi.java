package com.xunw.jxjy.feign.micro.feign;/*
@version 1.0
@Description直播微服务接口
<AUTHOR>
@Date   2024/2/27
*/

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xunw.jxjy.feign.micro.config.FeignConfig;
import com.xunw.jxjy.feign.micro.dto.ResultMsg;
import com.xunw.jxjy.feign.micro.dto.live.*;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;


@FeignClient(contextId = "remoteLiveCourseApiService", name = "learning-live-service", configuration = FeignConfig.class)
public interface LiveCourseApi {

    /**新增直播公开课
     * @param model
     * @return
     */
    @RequestMapping(value = "/api/courseLive/add", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean add(@RequestBody JSONObject model);

    /**
     * @param id 根据直播id查询直播课详情
     * @return
     */
    @RequestMapping(value = "/api/courseLive/getById", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    FeignCourseLiveDTO getById(@RequestParam(required = false, value = "id") String id);

    /**修改直播
     * @param
     * @return
     */
    @RequestMapping(value = "/api/courseLive/edit", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean edit(@RequestBody JSONObject model);

    /** 查询直播课时详情
     * @param lessonId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/getLessonById", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    FeignLiveLessonDTO getLessonById(@RequestParam(required = false, value = "lessonId") String lessonId);

    /** 删除直播
     * @param liveId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/remove", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean removeLiveById(@RequestParam(required = false, value = "liveId") String liveId);

    /**删除直播课时
     * @param lessonId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/removeLesson", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    String removeLesson(@RequestParam(required = false, value = "lessonId") String lessonId);

    /** 刷新推流地址
     * @param lessonId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/refresh", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean refreshLiveLesson(@RequestParam(required = false, value = "lessonId") String lessonId);

    /** 根据直播课获取推流地址
     * @param lessonId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/getStreamUrl", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Map<String, Object> getStreamUrl(@RequestParam(required = false, value = "lessonId") String lessonId);

    /** 外部推流
     * @param lessonId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/thirdPush", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean thirdPush(@RequestParam(required = false, value = "lessonId") String lessonId);

    /** 添加学习资料
     * @param jsonObject
     * @return
     */
    @RequestMapping(value = "/api/information/saveInformation", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean addStudyInformation(@RequestBody JSONObject jsonObject);

    /** 查询学习资料
     * @param lessonId
     * @param current
     * @param size
     * @return
     */
    @RequestMapping(value = "/api/information/selectInformationList", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Page<FeignLiveLearningMaterialsDTO> selectInformationList(@RequestParam(required = false, value = "lessonId") String lessonId,
                                                              @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                                                              @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    /**删除学习资料
     * @param id
     * @return
     */
    @RequestMapping(value = "/api/information/removeInformation", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean removeInformation(@RequestParam(required = false, value = "id") String id);


    /**开始直播
     * @param liveId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/goLive", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean goLive(@RequestParam(required = false, value = "liveId") String liveId);

    /**课间休息
     * @param liveId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/breakLive", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean offline(@RequestParam(required = false, value = "liveId")String liveId);

    /**恢复直播
     * @param liveId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/recoverLive", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean recovery(@RequestParam(required = false, value = "liveId")String liveId);

    /**结束直播
     * @param liveId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/endLive", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean stop(@RequestParam(required = false, value = "liveId")String liveId ,@RequestParam(required = false, value = "notifyUrl")String notifyUrl);

    /**根据直播章节id获取courseId
     * @param chapterId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/getCourseId", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    String getCourseId(@RequestParam(required = false, value = "chapterId")String chapterId);

    /** 获取课堂信息
     * @param lessonId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/getLiveBaseInfo", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    JSONObject getLiveBaseInfo(@RequestParam(required = false, value = "lessonId")String lessonId);

    /** 保存学习记录
     * @param lessonId
     * @return
     */
    @RequestMapping(value = "/api/watchRecord/saveLearnByStudent", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean saveStudyRecoding(@RequestParam(required = false, value = "lessonId")String lessonId,
                                @RequestParam(required = false, value = "orgId") String orgId);

    /**添加学习笔记
     * @param object
     * @return
     */
    @RequestMapping(value = "/api/liveNote/addLearnNote", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean addLearnNote(@RequestBody JSONObject object);

    /**获取直播学习笔记
     * @param id
     * @return
     */
    @RequestMapping(value = "/api/liveNote/getLearnNotes", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    List<FeignLiveNoteDTO> getLearnNotes(@RequestParam(required = false, value = "id") String id);

    /**删除直播学习笔记
     * @param id
     * @return
     */
    @RequestMapping(value = "/api/liveNote/removeLearnNotes", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean removeLearnNotes(@RequestParam(required = false, value = "id") String id);


    /**直播评分保存
     * @param object
     * @return
     */
    @RequestMapping(value = "/api/courseLive/addLearnStar", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean addLearnComment(JSONObject object);


    /**获取在线用户列表
     * @param liveId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/getOnlineUserList", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Map<String, Object> getOnlineUserList(@RequestParam(required = false, value = "liveId")String liveId);

    /**获取学习纪录列表
     * @param
     * @return
     */
    @RequestMapping(value = "/api/watchRecord/getLearnByStudent", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Page<FeignLiveRecordDTO> getLearnList(@RequestParam(required = false, value = "lessonId")String lessonId);

    /**直播生成回放回调地址
     * @param param
     * @return
     */
    @RequestMapping(value = "/api/courseLive/notifyReplayUrl", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean notifyReplayUrl(@RequestBody JSONObject param);


    /**获取已经有学习记录的直播课
     * @param orgId
     * @return
     */
    @RequestMapping(value = "/api/watchRecord/getMyLive", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    List<FeignCourseLiveDTO> getMyLives(@RequestParam(required = false, value = "orgId")Long orgId);

    /**删除直播章节
     * @param chapterId
     * @return
     */
    @RequestMapping(value = "/api/courseLive/removeChapter", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    String removeChapter(@RequestParam(required = false, value = "chapterId")String chapterId);
}
