package com.xunw.jxjy.feign.micro.dto.courseware;

import com.xunw.jxjy.feign.micro.dto.exam.FeignExamPaperDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 课件主表
 */
@Data
public class FeignCoursewareDTO implements Serializable {

    private static final long serialVersionUID = 3204772325773912880L;

    /**
     * 主键
     */
    private String id;

    /**
     * 讲师
     */
    private String teacherName;

    /**
     * 讲师
     */
    private String teacher;

    /**
     * 讲师
     */
    private String lecturer;

    /**
     * 讲师照片
     */
    private String lecturerPhoto;

    /**
     * 星级
     */
    private Integer star;

    /**
     * 状态
     */
    private String status;

    /**
     * 来源
     */
    private String source;

    /**
     * 介绍
     */
    private String remark;

    /**
     * 是否有随堂练习(1是 0 否)
     */
    private Integer isTest;

    /**
     * 随堂练习抽题数量
     */
    private Integer extNum;

    /**
     * 课程id
     */
    private String  courseId;

    /**
     * 平台代码
     */
    private String  appId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String  creator;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 修改人
     */
    private String modifyor;


    /**
     * 机构id
     */
    private String orgId;

    /**
     * 课程章节
     */
    private List<FeignCoursewareChapterDTO> chapters;

    private Integer chapterNum;

    private Integer lessonNum;

    private Integer countMin;


    /**
     * 业务需要字段  start -------------------------------------
     */

    private Double countTime;


    private String introduce;

    private String isPay;

    private List<Map<String, Object>> relatedCourse;

    /**
     * 云平台课件id数组
     */
    private List<String> examCoursewareIds;

    private List<FeignExamPaperDTO> questions;


    private String batchId;

    private BigDecimal originalPrice;

    private BigDecimal discountPrice;

    private Integer purchaseCount;

    private Integer isBoutique;

    private Integer ownership;

    private String imgUrl;

    private String courseCode;

    private String courseName;

    /**
     * 业务需要字段  end -------------------------------------
     */

}
