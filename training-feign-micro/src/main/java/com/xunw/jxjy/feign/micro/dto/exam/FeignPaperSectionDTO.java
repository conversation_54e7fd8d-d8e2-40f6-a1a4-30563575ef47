package com.xunw.jxjy.feign.micro.dto.exam;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FeignPaperSectionDTO implements Serializable {

    private static final long serialVersionUID = 8326868305928614163L;

    //序号
    private String id;

    private String name;

    private String remark;
    //每小题多少分
    private  Integer perScore;
    // 章节试题
    private List<FeignPaperQuestionDTO> questions;

    /**
    *@Description: 模板试卷专用
    */
    private String questionType;  //题型
    private String difficulty; //难度
    private String questionDbId; //题库id
    private Integer score; //每题分数
    private Integer count; //抽题数量

    /**
     *@Description: 卷库组卷专用
     */
    private String realType;  //题型（卷库）
    private String difficultys; //难度（卷库）
    private String dbIds; //卷库id


}
