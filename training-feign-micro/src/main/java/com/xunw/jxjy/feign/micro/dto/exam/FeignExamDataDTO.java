package com.xunw.jxjy.feign.micro.dto.exam;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * @TableName es_b_exam_data
 */
@Data
public class FeignExamDataDTO implements Serializable {

    private static final long serialVersionUID = -5978291850730785546L;

    /**
     *
     */
    private String id;

    /**
     * 耗时（秒）
     */
    private long duration;

    /**
     * 考试开始时间
     */
    private String startTime;

    /**
     * 考试结束时间
     */
    private String endTime;

    /**
     * 允许考试次数
     */
    private Integer frequency = 1;

    /**
     * 试卷id
     */
    private String paperId;

    /**
     * 状态，NOTSTART-未开始、STARTED-进行中、SUBMITTED-已提交、REVISED-已批改、CLOSED-已关闭
     */
    private String status;

    /**
     * 得分
     */
    private Integer score;

    /**
     * 批改人
     */
    private String checkUserId;

    /**
     * 作答用户id
     */
    private String studentId;

    /**
     * 系统标识
     */
    private String appId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createdTime;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    private String lastModifiedTime;

    /**
     * 归属机构id
     */
    private String belongOrgId;


    private String name;

    private String remark;

    private String category;

    private String batchId;

    private BigDecimal originalPrice;

    private BigDecimal discountPrice;

    private Integer purchaseCount;

    private Integer isBoutique;

    private Integer ownership;

    private String courseId;

    private String imgUrl;

    private String courseCode;

    private String courseName;

    private String orgId;

    private Long schoolId;

    private String schoolCode;


    private String schoolName;


    private Long regGroupId;
    private String regGroupCode;


    private String regGroupName;

    private String groupName;
    private String groupPointName;


    private String zkzNo;

    private String certino;

    private String studentName;
    
    private String phone;

}
