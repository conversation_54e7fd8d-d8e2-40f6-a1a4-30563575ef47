package com.xunw.jxjy.feign.micro.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description
 * <AUTHOR>
 * @date 2024/1/3 14:32
 */
@Data
public class CoursewareVo implements Serializable {
    private static final long serialVersionUID = 5881859423131390439L;

    private String id;
    private String courseId;

    private String lecturer;

    private String lecturerPhoto;

    private Integer star;

    private String status;

    private String source;

    private String remark;

    private Integer isTest;

    private Long orgId;

    private Integer extNum;

    private String chapters;

    public CoursewareVo() {
    }

    public CoursewareVo(String courseId, String lecturer, String lecturerPhoto, Integer star, String status, String source, String remark, Integer isTest, Long orgId, Integer extNum, String chapters) {
        this.courseId = courseId;
        this.lecturer = lecturer;
        this.lecturerPhoto = lecturerPhoto;
        this.star = star;
        this.status = status;
        this.source = source;
        this.remark = remark;
        this.isTest = isTest;
        this.orgId = orgId;
        this.extNum = extNum;
        this.chapters = chapters;
    }

    public CoursewareVo(String id, String courseId, String lecturer, String lecturerPhoto, Integer star, String status, String source, String remark, Integer isTest, Long orgId, Integer extNum, String chapters) {
        this.id = id;
        this.courseId = courseId;
        this.lecturer = lecturer;
        this.lecturerPhoto = lecturerPhoto;
        this.star = star;
        this.status = status;
        this.source = source;
        this.remark = remark;
        this.isTest = isTest;
        this.orgId = orgId;
        this.extNum = extNum;
        this.chapters = chapters;
    }

}
