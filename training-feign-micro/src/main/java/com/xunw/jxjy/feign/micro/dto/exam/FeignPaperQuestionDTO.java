package com.xunw.jxjy.feign.micro.dto.exam;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class FeignPaperQuestionDTO implements Serializable {

	private static final long serialVersionUID = 7340466413619615234L;

	// 试题编号
	private String id;
	// 试题类型
	private String type;
	// 试题题干
	private String content;
	// 答案
	private String answer;
	// 试题解析
	private String analysis;
	private String realType;
	private String difficulty;
	// 分数
	private Integer score = 0;
	private String dbId;
	private String appId;

	private String source;

	private String isCorrected;

	private String status;

	private List<Option> options;

	private List<Blank> blanks;

	//是否混杂模式,即：答案不分顺序
	private String isComplex = "0";

	private List<FeignPaperQuestionDTO> children = new ArrayList<>();

}
