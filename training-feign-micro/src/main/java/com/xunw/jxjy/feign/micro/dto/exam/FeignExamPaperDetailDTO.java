package com.xunw.jxjy.feign.micro.dto.exam;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Description 试卷/作业详情
 * <AUTHOR>
 * @date 2024/12/19 14:14
 */
@Data
public class FeignExamPaperDetailDTO implements Serializable {

    private static final long serialVersionUID = -740879830692133637L;

    private FeignExamPaperDTO paper;

    private JSONObject answers;

    private JSONObject checks;

    private FeignExamDataDTO exam;

    private Long shortestSubmitTime;

    private List<FeignAnswerCardDTO> answerCards;

    /**
     * 作答开始时间
     */
    private String startTime;

    private String id;

    private String status;


}
