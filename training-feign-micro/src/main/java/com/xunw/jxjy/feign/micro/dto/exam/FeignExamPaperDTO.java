package com.xunw.jxjy.feign.micro.dto.exam;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @TableName es_b_exam_pape
 */

@Data
public class FeignExamPaperDTO implements Serializable {

    private static final long serialVersionUID = -7502540751747999008L;

    /**
     * 考试试卷id
     */
    private String id;

    /**
     * 试卷名称
     */
    private String name;

    /**
     * 考试开始时间
     */
    private String startTime;

    /**
     * 考试结束时间
     */
    private String endTime;

    /**
     * 考试时长（分钟）
     */
    private Integer duration;

    /**
     * 试卷总分
     */
    private Integer totalScore;

    /**
     * 及格分数
     */
    private Integer passScore;

    /**
     * 分数
     */
    private Integer score;

    /**
     * 是否主客观 0否,1是
     */
    private String isObject;

    /**
     * 说明
     */
    private String remark;

    /**
     * 试卷状态：DRAFT("草稿"),
                DISABLED("禁用"),
                ENABLE("启用");

        学生查询，是作答状态
     */
    private String status;

    /**
     * 试卷分类
     */
    private String category;

    /**
     * 是否开放答案和解析
     */
    private Integer answerExpose;

    /**
     * 是否开启抓拍
     */
    private Integer needPhoto;

    /**
     * 抓拍间隔时间（秒）
     */
    private Integer photoInterval;

    /**
     * 试题排序方式,0正常,1随机
     */
    private Integer orderType;

    /**
     * 试卷类型,0普通试卷,1随机试卷，2模板试卷
     */
    private Integer paperType;

    /**
     * 是否启用人脸比对
     */
    private Integer faceComparison;

    /**
     * 人脸比对阈值
     */
    private String threshold;

    /**
     * 是否整卷展示 0不是 1是
     */
    private String showMode;

    /**
     * 系统标识
     */
    private String appId;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * 课程id
     */
    private String courseId;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createdTime;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */

    private String lastModifiedTime;

    /**
     * 验证码
     */
    private String verify;

    /**
     * 是否允许手机考试
     */
    private String isAllowPhone;

    /**
     * 面授MIANSHOU、在线课堂ZXKT、课件学习KJXX、实践技能SJJN、成教ADULT、培训TRAIN
     */
    private String bizType;

    private String showAnswer;

    private String showTime;

    private String  ipStage;

    //是否是模拟考试
    private String isSimulate;

    /**
     * 试题数量
     */
    private Integer questionNum;

    private List<FeignPaperSectionDTO> sections = new ArrayList<>();

    private List<FeignPaperQuestionDTO> questions = new ArrayList<>();


    private String examDataId;

    private Integer examDuration;

    private String realityStartTime;

    private String realityEndTime;


    /**
     * 业务需要字段  start -------------------------------------
     */
    private String courseCode;

    private String courseName;

    private JSONObject answers;

    private JSONObject checks;

    /**
     * 业务需要字段  end -------------------------------------
     */


}
