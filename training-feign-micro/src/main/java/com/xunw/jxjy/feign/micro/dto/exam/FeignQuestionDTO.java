package com.xunw.jxjy.feign.micro.dto.exam;

import lombok.Data;

import java.io.Serializable;


/**
 * 试题
 *
 * @TableName es_b_question
 */
@Data
public class FeignQuestionDTO implements Serializable {

    private static final long serialVersionUID = -1399202277209809666L;

    /**
     * id
     */
    private String id;

    /**
     * 所属于题库
     */
    private String dbId;

    /**
     * 题干
     */
    private String content;

    /**
     * 题型枚举
     */
    private String type;

    /**
     * 真实题型
     */
    private String realType;

    /**
     * 难度枚举
     */
    private String difficulty;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否真题
     */
    private String isOldPaper;

    /**
     * 试题来源
     */
    private String source;

    /**
     * 套题父id
     */
    private String parentId;

    /**
     * 系统标识
     */
    private String appId;

    /**
     * 客观题答案
     */
    private String answer;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private String createdTime;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    private String lastModifiedTime;

    /**
     * 是否正确试题(-1 疑似错题;0未检查;1正确试题)
     */
    private Integer isCorrected;

    /**
     * 试题序号，套题才有
     */
    private Integer no;

    private String wrongReason;

    private Object data;

    private String studentAnswer;//学生答案

    private Integer dbPracticeIsTrue;//题库练习是否正确0否1是

    private String dbName;

    private String courseId;

    private String orgId;

    private Boolean isEdit = false;


}

