package com.xunw.jxjy.feign.micro.feign;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xunw.jxjy.feign.micro.config.FeignConfig;
import com.xunw.jxjy.feign.micro.dto.exam.*;
import feign.Headers;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@FeignClient(contextId = "remoteExamServiceApiService", name = "exam-service", configuration = FeignConfig.class)
public interface ExamServiceApi {


    @RequestMapping(value = "api/paper/list", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Page<FeignExamPaperDTO> examPaperList(@RequestParam(required = false, value = "name") Object name,
                                          @RequestParam(required = false, value = "category") Object category,
                                          @RequestParam(required = false, value = "orgIds") Object orgIds,
                                          @RequestParam(required = false, value = "status") Object status,
                                          @RequestParam(required = false, value = "courseId") Object courseId,
                                          @RequestParam(required = false, value = "isObject") Object isObject,
                                          @RequestParam(required = false, value = "appId") Object appId,
                                          @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                                          @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping(value = "api/student/paper/list", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Page<FeignExamPaperDTO> examStudentPaperList(@RequestParam(required = false, value = "name") Object name,
                                   @RequestParam(required = false, value = "status") Object status,
                                   @RequestParam(required = false, value = "courseId") Object courseId,
                                   @RequestParam(required = false, value = "appId") Object appId,
                                   @RequestParam(required = false, value = "studentId") Object studentId,
                                   @RequestParam(required = false, value = "batchId") Object batchId,
                                   @RequestParam(required = false, value = "bizType") Object bizType,
                                   @RequestParam(required = false, value = "category") Object category,
                                   @RequestParam(required = false, value = "courseIds") Object courseIds,
                                   @RequestParam(required = false, value = "isDone") Object isDone,
                                   @RequestParam(required = false, value = "paperId") Object paperId,
                                   @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                                   @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("api/student/oldPaper/list")
    @Headers({"Content-Type=multipart/form-data"})
    Page<FeignOldPaperDTO> examStudentOldPaperList(@RequestParam(required = false, value = "courseIds") Object courseIds,
                                                   @RequestParam(required = false, value = "courseId") Object courseId,
                                                   @RequestParam(required = false, value = "studentId") Object studentId,
                                                   @RequestParam(required = false, value = "paperId") Object paperId,
                                                   @RequestParam(required = false, value = "appId") Object appId);

    @RequestMapping("api/student/oldPaper/redoExam")
    @Headers({"Content-Type=multipart/form-data"})
    Integer examOldPaperRedoExam(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("api/student/oldPaper/detail")
    @Headers({"Content-Type=multipart/form-data"})
    FeignOldPaperDetailDTO examOldPaperExamDetail(@RequestParam(required = false, value = "id") String id,
                                                             @RequestParam(required = false, value = "paperId") String paperId);

    @RequestMapping("/api/student/exam/detail")
    @Headers({"Content-Type=multipart/form-data"})
    FeignExamPaperDetailDTO examExamDetail(@RequestParam(required = false, value = "id") String examDataId,
                                           @RequestParam(required = false, value = "paperId") String paperId);

    @RequestMapping("api/student/redoExam")
    @Headers({"Content-Type=multipart/form-data"})
    FeignExamDataDTO examPaperRedoExam(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("api/student/oldPaper/start")
    @Headers({"Content-Type=multipart/form-data"})
    FeignOldPaperDetailDTO examStudentOldPaperStart(@RequestParam(required = false, value = "paperId") Object paperId,
                                       @RequestParam(required = false, value = "examDataId") Object examDataId,
                                       @RequestParam(required = false, value = "shortestSubmitTime") Object shortestSubmitTime,
                                       @RequestParam(required = false, value = "orgId") Object orgId);

    @RequestMapping("api/student/exam/start")
    @Headers({"Content-Type=multipart/form-data"})
    FeignExamPaperDetailDTO examStudentStart(@RequestParam(required = false, value = "paperId") Object paperId,
                               @RequestParam(required = false, value = "examDataId") Object examDataId,
                               @RequestParam(required = false, value = "shortestSubmitTime") Object shortestSubmitTime,
                               @RequestParam(required = false,value = "orgId") String orgId);

    @RequestMapping(value = "api/questionDb/add", method = RequestMethod.POST)
    @Headers({"Content-Type=application/json"})
    FeignQuestionDbDTO examQuestionDbAdd(@RequestBody JSONObject json);

    @RequestMapping(value = "api/questionDb/update", method = RequestMethod.POST)
    @Headers({"Content-Type=application/json"})
    FeignQuestionDbDTO examQuestionDbUpdate(@RequestBody JSONObject json);

    @RequestMapping("api/questionDb/delete")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean examQuestionDbDelete(@RequestParam(required = false, value = "ids") String ids);

    @RequestMapping(value = "/api/questionDb/list", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Page<FeignQuestionDbDTO> examQuestionDbList(@RequestParam(required = false, value = "name") Object name,
                                 @RequestParam(required = false, value = "status") Object status,
                                 @RequestParam(required = false, value = "isOldPaper") Object isOldPaper,
                                 @RequestParam(required = false, value = "courseId") Object courseId,
                                 @RequestParam(required = false, value = "courseName") Object courseName,
                                 @RequestParam(required = false, value = "orgId") Object orgId,
                                 @RequestParam(required = false, value = "orgIds") Object orgIds,//多个值逗号分开  田军
                                 @RequestParam(required = false, value = "idsIn") Object idsIn,
                                 @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                                 @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/questionDb/preview")
    @Headers({"Content-Type=multipart/form-data"})
    FeignQuestionDbDTO questionDbPreview(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("/api/questionDb/questionPreview")
    @Headers({"Content-Type=multipart/form-data"})
    List<FeignQuestionDTO> questionQuestionDbPreview(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("/api/questionDb/courses")
    @Headers({"Content-Type=multipart/form-data"})
    List<FeignPaperQuestionDTO> examQuestionDbCourses();

    @RequestMapping(value = "api/questionDb/packagePaper", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Boolean examQuestionDbPackagePaper(@RequestParam(required = false, value = "id") Object id,
                                         @RequestParam(required = false, value = "category") Object category,
                                         @RequestParam(required = false, value = "paperCount") Object paperCount,
                                         @RequestParam(required = false, value = "count") Object count,
                                         @RequestParam(required = false, value = "score") Object score,
                                         @RequestParam(required = false, value = "isRepo") Object isRepo,
                                         @RequestParam(required = false, value = "orgId") Object orgId);

    @RequestMapping(value = "api/oldPaper/list", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Page<FeignOldPaperDTO> examOldPaperList(@RequestParam(required = false, value = "name") Object name,
                               @RequestParam(required = false, value = "status") Object status,
                               @RequestParam(required = false, value = "courseId") Object courseId,
                               @RequestParam(required = false, value = "isObject") Object isObject,
                               @RequestParam(required = false, value = "yearStart") Object yearStart,
                               @RequestParam(required = false, value = "monthStart") Object monthStart,
                               @RequestParam(required = false, value = "yearEnd") Object yearEnd,
                               @RequestParam(required = false, value = "monthEnd") Object monthEnd,
                               @RequestParam(required = false, value = "province") Object province,
                               @RequestParam(required = false, value = "appId") Object appId,
                               @RequestParam(required = false, value = "studentId") Object studentId,
                               @RequestParam(required = false, defaultValue = "1", value = "current") Long current,
                               @RequestParam(required = false, defaultValue = "10", value = "size") Long size);

    @RequestMapping("api/oldPaper/preview")
    @Headers({"Content-Type=multipart/form-data"})
    FeignOldPaperDTO examOldPaperPreview(@RequestParam(required = false, value = "id") String id);

    @RequestMapping(value = "api/oldPaper/add", method = RequestMethod.POST)
    @Headers({"Content-Type=application/json"})
    FeignOldPaperDTO examOldPaperAdd(@RequestBody JSONObject json);

    @RequestMapping(value = "api/question/add", method = RequestMethod.POST)
    @Headers({"Content-Type=application/json"})
    FeignQuestionDTO examQuestionAdd(@RequestBody JSONObject json);

    @RequestMapping("/api/question/preview")
    @Headers({"Content-Type=multipart/form-data"})
    FeignQuestionDTO questionGetQuesDetailsById(@RequestParam(required = false, value = "id") String id);

    @RequestMapping(value = "/api/question/list", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Page<FeignQuestionDTO> questionList(@RequestParam(required = false, value = "courseId") Object courseId,
                           @RequestParam(required = false, value = "courseName") Object courseName,
                           @RequestParam(required = false, value = "realType") Object realType,
                           @RequestParam(required = false, value = "dbId") Object dbId,
                           @RequestParam(required = false, value = "difficulty") Object difficulty,
                           @RequestParam(required = false, value = "content") Object content,
                           @RequestParam(required = false, value = "type") Object type,
                           @RequestParam(required = false, value = "status") Object status,
                           @RequestParam(required = false, value = "isOldPaper") Object isOldPaper,
                           @RequestParam(required = false, value = "source") Object source,
                           @RequestParam(required = false, value = "parentId") Object parentId,
                           @RequestParam(required = false, value = "answer") Object answer,
                           @RequestParam(required = false, value = "createdBy") Object createdBy,
                           @RequestParam(required = false, value = "createdTime") Object createdTime,
                           @RequestParam(required = false, value = "lastModifiedBy") Object lastModifiedBy,
                           @RequestParam(required = false, value = "lastModifiedTime") Object lastModifiedTime,
                           @RequestParam(required = false, value = "isCorrected") Object isCorrected,
                           @RequestParam(required = false, value = "no") Object no,
                           @RequestParam(required = false, value = "orgIds") Object orgIds,
                           @RequestParam(required = false, value = "dbIds") Object dbIds,
                           @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                           @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping(value = "/api/question/count", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Integer questionCount(@RequestParam(required = false, value = "courseId") Object courseId,
                            @RequestParam(required = false, value = "courseName") Object courseName,
                            @RequestParam(required = false, value = "realType") Object realType,
                            @RequestParam(required = false, value = "dbId") Object dbId,
                            @RequestParam(required = false, value = "difficulty") Object difficulty,
                            @RequestParam(required = false, value = "content") Object content,
                            @RequestParam(required = false, value = "type") Object type,
                            @RequestParam(required = false, value = "status") Object status,
                            @RequestParam(required = false, value = "isOldPaper") Object isOldPaper,
                            @RequestParam(required = false, value = "source") Object source,
                            @RequestParam(required = false, value = "parentId") Object parentId,
                            @RequestParam(required = false, value = "answer") Object answer,
                            @RequestParam(required = false, value = "createdBy") Object createdBy,
                            @RequestParam(required = false, value = "createdTime") Object createdTime,
                            @RequestParam(required = false, value = "lastModifiedBy") Object lastModifiedBy,
                            @RequestParam(required = false, value = "lastModifiedTime") Object lastModifiedTime,
                            @RequestParam(required = false, value = "isCorrected") Object isCorrected,
                            @RequestParam(required = false, value = "no") Object no,
                            @RequestParam(required = false, value = "orgId") Object orgId,
                            @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                            @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/question/selectRealType")
    @Headers({"Content-Type=multipart/form-data"})
    List<Map<String, Object>> questionSelectRealType();

    @RequestMapping(value = "api/question/edit", method = RequestMethod.POST)
    @Headers({"Content-Type=application/json"})
    FeignQuestionDTO examQuestionEdit(@RequestBody JSONObject json);

    @RequestMapping(value = "api/question/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Integer examQuestionImport(@RequestPart(value = "file", required = false) MultipartFile file,
                                 @RequestParam(required = false, value = "dbId") Object dbId,
                                 @RequestParam(required = false, value = "parentId") Object parentId,
                                 @RequestParam(required = false, value = "isOldPaper") Object isOldPaper,
                                 @RequestParam(required = false, value = "type") Object type,
                                 @RequestParam(required = false, value = "status") Object status);

    @RequestMapping(value = "api/oldPaper/package", method = RequestMethod.POST)
    @Headers({"Content-Type=application/json"})
    Boolean examOldPaperPackage(@RequestBody JSONObject json);

    @RequestMapping("api/question/delete")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean examQuestionDelete(@RequestParam(required = false, value = "ids") String ids);

    @RequestMapping("api/paper/delete")
    @Headers({"Content-Type=multipart/form-data"})
    Integer examPaperDelete(@RequestParam(required = false, value = "ids") String ids);

    @RequestMapping("/api/paper/getById")
    @Headers({"Content-Type=multipart/form-data"})
    FeignExamPaperDTO examPaperGetById(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("api/oldPaper/delete")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean examOldPaperDelete(@RequestParam(required = false, value = "ids") String ids);

    @RequestMapping("api/oldPaper/setStatus")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean examOldPaperSetStatus(@RequestParam(required = false, value = "id") String id,
                                    @RequestParam(required = false, value = "status") String status);

    @RequestMapping(value = "api/paper/addAndAuto", method = RequestMethod.POST)
    @Headers({"Content-Type=application/json"})
    FeignExamPaperDTO examPaperAddAndAuto(@RequestBody JSONObject json);

    @RequestMapping(value = "api/paper/updatePaperName", method = RequestMethod.POST)
    @Headers({"Content-Type=application/json"})
    Boolean examPaperUpdate(@RequestBody JSONObject json);

    @RequestMapping(value = "api/student/oldPaper/examSave", method = RequestMethod.POST)
    @Headers({"Content-Type=application/json"})
    Integer examOldPaperExamSave(@RequestBody JSONObject json);

    @RequestMapping("api/student/oldPaper/examSubmit")
    @Headers({"Content-Type=multipart/form-data"})
    FeignOldDataDTO examOldPaperExamSubmit(@RequestParam(required = false, value = "id") String id);

    @RequestMapping(value = "api/student/examSave", method = RequestMethod.POST)
    @Headers({"Content-Type=application/json"})
    Integer examPaperSave(@RequestBody JSONObject json);

    @RequestMapping("api/student/examSubmit")
    @Headers({"Content-Type=multipart/form-data"})
    FeignExamDataDTO examPaperSubmit(@RequestParam(required = false, value = "id") String id);

    @RequestMapping(value = "api/paper/batchSetOpenTimeSave", method = RequestMethod.POST)
    @Headers({"Content-Type=application/json"})
    Boolean examPaperBatchSetOpenTimeSave(@RequestBody JSONObject json);

    @RequestMapping(value = "api/exam/list", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Page<FeignExamDataDTO> examExamList(@RequestParam(required = false, value = "orgId") Object orgId,
                           @RequestParam(required = false, value = "batchId") Object batchId,
                           @RequestParam(required = false, value = "courseId") Object courseId,
                           @RequestParam(required = false, value = "courseName") Object courseName,
                           @RequestParam(required = false, value = "keyword") Object keyword,
                           @RequestParam(required = false, value = "paperId") Object paperId,
                           @RequestParam(required = false, value = "category") Object category,
                           @RequestParam(required = false, value = "paperName") Object paperName,
                           @RequestParam(required = false, value = "studentId") Object studentId,
                           @RequestParam(required = false, value = "status") Object status,
                           @RequestParam(required = false, value = "minScore") Object minScore,
                           @RequestParam(required = false, value = "maxScore") Object maxScore,
                           @RequestParam(required = false, value = "bizType") Object bizType,
                           @RequestParam(required = false, value = "appId") Object appId,
                           @RequestParam(required = false,value = "belongOrgId")String belongOrgId,
                           @RequestParam(required = false,value = "courseIds")String courseIds,
                           @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                           @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping(value = "api/oldPaper/examList", method = RequestMethod.POST)
    @Headers({"Content-Type=multipart/form-data"})
    Page<FeignOldDataDTO> examOldPaperExamList(@RequestParam(required = false, value = "orgId") Object orgId,
                                   @RequestParam(required = false, value = "batchId") Object batchId,
                                   @RequestParam(required = false, value = "courseId") Object courseId,
                                   @RequestParam(required = false, value = "courseName") Object courseName,
                                   @RequestParam(required = false, value = "keyword") Object keyword,
                                   @RequestParam(required = false, value = "paperId") Object paperId,
                                   @RequestParam(required = false, value = "category") Object category,
                                   @RequestParam(required = false, value = "paperName") Object paperName,
                                   @RequestParam(required = false, value = "studentId") Object studentId,
                                   @RequestParam(required = false, value = "status") Object status,
                                   @RequestParam(required = false, value = "minScore") Object minScore,
                                   @RequestParam(required = false, value = "maxScore") Object maxScore,
                                   @RequestParam(required = false, value = "bizType") Object bizType,
                                   @RequestParam(required = false, value = "appId") Object appId, 
                                   @RequestParam(required = false, value = "paperIds") Object paperIds,
                                   @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                                   @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping(value = "/api/questionDb/export", method = RequestMethod.POST, consumes = "application/json;charset=UTF-8")
    @Headers({"Content-Type=multipart/form-data"})
    Response exportQuestionDb(@RequestParam(required = false, value = "id") Object id,
                              @RequestParam(required = false, value = "name") Object name,
                              @RequestParam(required = false, value = "orgId") Object orgId);

    @RequestMapping("/api/question/getChildByPid")
    @Headers({"Content-Type=multipart/form-data"})
    List<FeignQuestionDTO> questionGetChildByPid(@RequestParam(required = false, value = "parentId") String parentId);

    @RequestMapping("/api/questionDb/select")
    @Headers({"Content-Type=multipart/form-data"})
    List<FeignQuestionDbDTO> examQuestionDbSelect(@RequestParam(required = false, value = "courseId") Object id,
                                   @RequestParam(required = false, value = "status") Object status,
                                   @RequestParam(required = false, value = "orgIds") Object orgIds);

    @RequestMapping("/api/questionDb/doAssign")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean batchAuthorize(@RequestParam(required = false, value = "dbId")String dbId,
                             @RequestParam(required = false, value = "orgId")String orgId);

    @RequestMapping("/api/questionDb/getAssign")
    @Headers({"Content-Type=multipart/form-data"})
    List<Long> getAssign(@RequestParam(required = false, value = "dbId")String dbId);


    @RequestMapping("/api/questionDb/deleteAssign")
    @Headers({"Content-Type=multipart/form-data"})
    Boolean deleteAssign(@RequestParam(required = false, value = "dbId")String dbId,
                           @RequestParam(required = false, value = "orgId")String orgId);

    @RequestMapping("/api/question/selectRealType")
    @Headers({"Content-Type=multipart/form-data"})
    List<Map> getQuestionRealTypeList();
}
