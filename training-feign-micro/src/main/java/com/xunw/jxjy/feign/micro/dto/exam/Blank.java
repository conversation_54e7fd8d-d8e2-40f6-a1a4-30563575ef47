package com.xunw.jxjy.feign.micro.dto.exam;

import lombok.Data;

import java.io.Serializable;


@Data
public class Blank implements Serializable {


	private static final long serialVersionUID = 622104418794472729L;
	private int id;
	private String name;
	private String value;

	public Blank(){

	}

	public Blank(int id, String name, String value) {
		this.id = id;
		this.name = name;
		this.value = value;
	}

}
