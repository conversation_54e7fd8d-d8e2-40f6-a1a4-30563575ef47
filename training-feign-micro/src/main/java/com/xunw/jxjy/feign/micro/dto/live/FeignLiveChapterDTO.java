package com.xunw.jxjy.feign.micro.dto.live;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ls_b_live_chapter
 * <AUTHOR>

@Data
public class FeignLiveChapterDTO implements Serializable {
    private static final long serialVersionUID = -4557853551469800868L;
    /**
     * id
     */
    private String id;

    /**
     * 直播id
     */
    private String liveId;

    /**
     * 章节序号
     */
    private Integer chapterNo;

    /**
     * 章节名称
     */
    private String chapterName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private String modifyTime;

    /**
     * 修改人
     */
    private String modifyor;

    private List<FeignLiveLessonDTO> lessons;


}
