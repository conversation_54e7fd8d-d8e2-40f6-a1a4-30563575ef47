package com.xunw.jxjy.feign.micro.dto.live;

import lombok.Data;

import java.io.Serializable;

/**
 * ls_b_live_note 记录直播学习笔记
 * <AUTHOR>
@Data
public class FeignLiveNoteDTO implements Serializable {

    private static final long serialVersionUID = 5261017192124338036L;

    /**
     * 主键
     */
    private String id;

    /**
     * 直播课时id
     */
    private String liveId;

    /**
     * 考生id
     */
    private String studentId;

    /**
     * 观看时间点（秒）
     */
    private Integer pointTime;

    /**
     * 笔记内容
     */
    private String content;

    /**
     * 记录时间
     */
    private String time;

}
