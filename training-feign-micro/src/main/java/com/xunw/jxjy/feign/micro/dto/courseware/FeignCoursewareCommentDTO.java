package com.xunw.jxjy.feign.micro.dto.courseware;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 记录课件的评论信息
 * @TableName ls_b_courseware_comment
 */
@Data
public class FeignCoursewareCommentDTO implements Serializable {

    private static final long serialVersionUID = 4308760340993112984L;

    /**
     * 主键
     */
    private String id;

    /**
     * 课时id
     */
    private String lessonId;

    /**
     * 学生id
     */
    private String studentId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 记录时间
     */
    private String time;

    /**
     * 评论人姓名
     */
    private String name;

}
