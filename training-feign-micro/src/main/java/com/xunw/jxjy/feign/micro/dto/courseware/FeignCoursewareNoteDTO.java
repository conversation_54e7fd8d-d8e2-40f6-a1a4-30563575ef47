package com.xunw.jxjy.feign.micro.dto.courseware;

import lombok.Data;

import java.io.Serializable;

/**
 * 记录课件学习笔记
 * @TableName ls_b_courseware_note
 */
@Data
public class FeignCoursewareNoteDTO implements Serializable {
    private static final long serialVersionUID = 7108216550336861559L;

    /**
     * 主键
     */
    private String id;

    /**
     * 课时id
     */
    private String lessonId;

    /**
     * 学生id
     */
    private String studentId;

    /**
     * 观看时间点（秒）
     */
    private Long pointTime;

    /**
     * 笔记内容
     */
    private String content;

    /**
     * 记录时间
     */
    private String time;

    /**
     * 做笔记的学员用户姓名
     */
    private String name;

}
