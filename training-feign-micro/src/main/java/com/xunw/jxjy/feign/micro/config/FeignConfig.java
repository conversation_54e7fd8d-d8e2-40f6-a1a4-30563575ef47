package com.xunw.jxjy.feign.micro.config;

import cn.hutool.core.util.StrUtil;
import com.xunw.cloud.common.core.constant.SecurityConstants;
import com.xunw.cloud.common.core.constant.enums.UserType;
import com.xunw.cloud.common.core.util.Constants;
import com.xunw.cloud.common.security.service.PigxUser;
import com.xunw.cloud.common.security.util.JWTUtils;
import com.xunw.cloud.common.security.util.SecurityUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.codec.Decoder;
import feign.optionals.OptionalDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.HttpMessageConverterCustomizer;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import static cn.hutool.core.text.CharSequenceUtil.trim;


/**
 * 调用公共微服务的 Feign配置
 * 使用FeignClient进行服务间调用，传递headers信息
 */
public class FeignConfig implements RequestInterceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(FeignConfig.class);

    @Override
    public void apply(RequestTemplate requestTemplate) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes attr = (ServletRequestAttributes) requestAttributes;
        HttpServletRequest request = attr.getRequest();
        String token = request.getHeader(Constants.AUTHORIZATION);
        String microJwtToken = "";
        if(StrUtil.isNotEmpty(token)){
            if(token.startsWith("Bearer ")){//管理端
                PigxUser pigxUser = SecurityUtils.getUser();
                if(pigxUser != null){
                    microJwtToken = JWTUtils.sign(pigxUser.getId().toString(), pigxUser.getUsername(), UserType.ADMIN.name(), pigxUser.getTenantId().toString(), Constants.APP_ID);
                }
            }
            else if(JWTUtils.verify(token)){//学生端
                microJwtToken = token;
            }
        }
        requestTemplate.header(Constants.AUTHORIZATION, microJwtToken);
        requestTemplate.header(Constants.APP_ID, Constants.APP_ID);
    }

    @Bean
    public Decoder decoder(ObjectFactory<HttpMessageConverters> msgConverters, ObjectProvider<HttpMessageConverterCustomizer> customizers) {
        return new OptionalDecoder((new ResponseEntityDecoder(new OrgFeignDecoder(new SpringDecoder(msgConverters, customizers)))));
    }
}
