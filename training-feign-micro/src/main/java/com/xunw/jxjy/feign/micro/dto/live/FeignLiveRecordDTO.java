package com.xunw.jxjy.feign.micro.dto.live;

import lombok.Data;

import java.io.Serializable;

/**
 * watch_record
 * <AUTHOR>
@Data
public class FeignLiveRecordDTO implements Serializable {

    private static final long serialVersionUID = 1218283714190309248L;

    /**
     * id
     */
    private String id;


    /**
     * 批次id
     */
    private String bathId;


    /**
     * 考生id
     */
    private String studentId;

    /**
     * 直播id
     */
    private String liveId;

    /**
     * 学习开始时间
     */
    private String startTime;

    /**
     * 学习结束时间
     */
    private String endTime;


    /**
     * 课时总时长
     */
    private Integer totalDuration;

    /**
     * 直播观看时长,，当且仅当视频资源类型是直播时可能有值值
     */
    private Integer liveDuration;

    /**
     * 录播观看时长,，当且仅当视频资源类型是直播时可能有值值
     */
    private Integer replayDuration;

    /**
     * 累计观看时长（单位：分钟）
     */
    private Integer duration;

    /**
     * 是否已学完;1 是,0 否
     */
    private Integer isFinished;

    /**
     * 更新时间
     */
    private String modifyTime;

    /**
     * 系统标识
     */
    private String appId;

    /**
     * 机构id
     */
    private String orgId;

    private String name;

    private String certiNo;

    private String zkzNo;


}
