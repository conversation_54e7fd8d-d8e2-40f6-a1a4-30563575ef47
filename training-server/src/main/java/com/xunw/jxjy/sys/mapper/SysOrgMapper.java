/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.xunw.jxjy.sys.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xunw.cloud.common.data.datascope.PigxBaseMapper;
import com.xunw.jxjy.feign.sys.entity.SysOrg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 租户
 *
 * <AUTHOR>
 * @date 2019-05-15 15:55:41
 */
@Mapper
public interface SysOrgMapper extends PigxBaseMapper<SysOrg> {

    List<SysOrg> getSysTenantPage(Page page, @Param("query") SysOrg sysTenant);

    List<SysOrg> getRelatedHostOrgByOrgId(String orgId);

    List<SysOrg> treeList(@Param("query") SysOrg sysOrg);

    SysOrg getTopOrg(String tenantId);
}
