/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.xunw.jxjy.sys.handler;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xunw.jxjy.feign.sys.dto.UserInfo;
import com.xunw.jxjy.feign.sys.entity.SysUser;

import com.xunw.jxjy.sys.service.ConnectService;
import com.xunw.jxjy.sys.service.SysUserService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/19
 *
 * 企业微信免密登录
 */
@Slf4j
@Component("WEIXIN_CP")
@AllArgsConstructor
public class WxCpLoginHandler extends AbstractLoginHandler {

	private ConnectService connectService;

	private final SysUserService sysUserService;

	/**
	 * 微信登录传入code
	 * <p>
	 * 通过code 调用qq 获取唯一标识
	 * @param code
	 * @return
	 */
	@Override
	@SneakyThrows
	public String identify(String code) {
		WxCpService wxCpService = new WxCpServiceImpl();
		wxCpService.setWxCpConfigStorage(connectService.getCpConfig());

		WxCpOauth2UserInfo userInfo = wxCpService.getOauth2Service().getUserInfo(code);
		log.info("企业微信返回报文:{}", userInfo);
		return userInfo.getUserId();
	}

	/**
	 * openId 获取用户信息
	 * @param openId
	 * @return
	 */
	@Override
	public UserInfo info(String openId) {
		SysUser user = sysUserService.getOne(Wrappers.<SysUser>query().lambda().eq(SysUser::getWxOpenid, openId));

		if (user == null) {
			log.info("企业微信未绑定:{}", openId);
			return null;
		}
		return sysUserService.findUserInfo(user);
	}

	/**
	 * 绑定逻辑
	 * @param user 用户实体
	 * @param identify 渠道返回唯一标识
	 * @return
	 */
	@Override
	public Boolean bind(SysUser user, String identify) {
		user.setWxOpenid(identify);
		sysUserService.updateById(user);
		return true;
	}

}
