/*
 * Copyright (c) 2020 pig4cloud Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.xunw.jxjy.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xunw.jxjy.feign.sys.entity.SysUserPost;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 用户岗位 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/19
 */
@Mapper
public interface SysUserPostMapper extends BaseMapper<SysUserPost> {

}
