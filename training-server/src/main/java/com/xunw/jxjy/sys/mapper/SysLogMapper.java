/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.xunw.jxjy.sys.mapper;

import com.xunw.cloud.common.data.datascope.PigxBaseMapper;
import com.xunw.jxjy.feign.sys.entity.SysLog;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-11-20
 */
@Mapper
public interface SysLogMapper extends PigxBaseMapper<SysLog> {

	/**
	 * 通过日志类型统计日志总数 返回一个包含日志类型和日志总数的列表
	 * @return List<Map<String, Object>>
	 */
	List<Map<String, Object>> selectLogSumByType();

}
