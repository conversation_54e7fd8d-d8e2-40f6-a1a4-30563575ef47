/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.xunw.jxjy.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xunw.cloud.common.core.util.R;
import com.xunw.jxjy.feign.sys.entity.SysPublicParam;

import java.util.Map;

/**
 * 公共参数配置
 *
 * <AUTHOR>
 * @date 2019-04-29
 */
public interface SysPublicParamService extends IService<SysPublicParam> {

	/**
	 * 通过key查询公共参数指定值
	 * @param publicKey
	 * @return
	 */
	String getSysPublicParamKeyToValue(String publicKey);

	/**
	 * 通过key查询公共参数指定值
	 * @param keys 参数列表
	 * @return Map
	 */
	Map<String, Object> getSysPublicParamsKeyToValue(String[] keys);

	/**
	 * 更新参数
	 * @param sysPublicParam
	 * @return
	 */
	R updateParam(SysPublicParam sysPublicParam);

	/**
	 * 删除参数
	 * @param publicIds 参数列表
	 * @return
	 */
	R removeParamByIds(Long[] publicIds);

	/**
	 * 同步缓存
	 * @return R
	 */
	R syncParamCache();

}
