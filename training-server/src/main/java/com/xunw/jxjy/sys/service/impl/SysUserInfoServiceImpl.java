/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 *
 */

package com.xunw.jxjy.sys.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunw.jxjy.feign.sys.entity.SysUserInfo;
import com.xunw.jxjy.sys.mapper.SysUserInfoMapper;
import com.xunw.jxjy.sys.service.SysUserInfoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 管理员用户信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysUserInfoServiceImpl extends ServiceImpl<SysUserInfoMapper, SysUserInfo> implements SysUserInfoService {

	/**
	 * 通过用户ID查询用户信息
	 * @param userId 用户ID
	 * @return 用户信息
	 */
	@Override
	public SysUserInfo getByUserId(String userId) {
		return baseMapper.selectByUserId(userId);
	}

	/**
	 * 保存或更新用户信息
	 * @param sysUserInfo 用户信息
	 * @return 是否成功
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean saveOrUpdateUserInfo(SysUserInfo sysUserInfo) {
		// 检查是否已存在该用户的信息
		SysUserInfo existingInfo = this.getByUserId(sysUserInfo.getUserId());
		
		if (existingInfo != null) {
			// 更新现有记录
			sysUserInfo.setId(existingInfo.getId());
			return this.updateById(sysUserInfo);
		} else {
			// 新增记录
			return this.save(sysUserInfo);
		}
	}

	/**
	 * 通过用户ID删除用户信息
	 * @param userId 用户ID
	 * @return 是否成功
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean removeByUserId(String userId) {
		return this.remove(Wrappers.<SysUserInfo>lambdaQuery().eq(SysUserInfo::getUserId, userId));
	}

	/**
	 * 批量查询用户信息
	 * @param userIds 用户ID列表
	 * @return 用户信息列表
	 */
	@Override
	public List<SysUserInfo> listByUserIds(List<String> userIds) {
		return baseMapper.selectByUserIds(userIds);
	}

	/**
	 * 根据教师类型查询用户信息
	 * @param teacherTypeId 教师类型ID
	 * @return 用户信息列表
	 */
	@Override
	public List<SysUserInfo> listByTeacherTypeId(String teacherTypeId) {
		return baseMapper.selectByTeacherTypeId(teacherTypeId);
	}

	/**
	 * 查询门户展示的用户信息
	 * @return 用户信息列表
	 */
	@Override
	public List<SysUserInfo> listPortalShowUsers() {
		return baseMapper.selectPortalShowUsers();
	}

}
