/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.xunw.jxjy.sys.mapper;

import com.xunw.cloud.common.data.datascope.PigxBaseMapper;
import com.xunw.jxjy.feign.sys.entity.SysOrgRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机构关系表
 *
 * <AUTHOR>
 * @date 2019-05-15 15:55:41
 */
@Mapper
public interface SysOrgRelationMapper extends PigxBaseMapper<SysOrgRelation> {

	/**
	 * 根据主办单位ID查询关联的机构列表
	 * @param hostOrgId 主办单位ID
	 * @return 机构关系列表
	 */
	List<SysOrgRelation> selectByHostOrgId(@Param("hostOrgId") String hostOrgId);

	/**
	 * 根据机构ID查询关联的主办单位列表
	 * @param orgId 机构ID
	 * @return 机构关系列表
	 */
	List<SysOrgRelation> selectByOrgId(@Param("orgId") String orgId);

	/**
	 * 根据主办单位ID和机构ID查询关系
	 * @param hostOrgId 主办单位ID
	 * @param orgId 机构ID
	 * @return 机构关系
	 */
	List<SysOrgRelation> selectByHostOrgIdAndOrgId(@Param("hostOrgId") String hostOrgId, @Param("orgId") String orgId);

	/**
	 * 根据关系类型查询机构关系列表
	 * @param relationType 关系类型
	 * @return 机构关系列表
	 */
	List<SysOrgRelation> selectByRelationType(@Param("relationType") String relationType);
}
