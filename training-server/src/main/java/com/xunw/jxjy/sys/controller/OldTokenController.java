package com.xunw.jxjy.sys.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xunw.cloud.common.core.util.R;
import com.xunw.cloud.common.data.resolver.ParamResolver;
import com.xunw.cloud.common.security.util.SecurityUtils;

import cn.hutool.http.HttpRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用于获取老的token
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/oldToken")
@Slf4j
public class OldTokenController {

	@GetMapping("/build")
	public R getOldToken(String roleType) {
//		String loginApi = ParamResolver.getStr("OLD_PLAT_LOGIN_URL", "老平台的登录地址");
		String loginApi = "http://**************:9058/htgl/login";
		log.info("老平台的登录地址:{}", loginApi);
		log.info("老平台的登录用户:{}", SecurityUtils.getUser().getUsername());
        //链式构建请求
        String result = HttpRequest.post(loginApi)
				.header("Content-Type", "multipart/form-data")
				.form("username", SecurityUtils.getUser().getUsername())
				.form("password", "Px!@1357902")
				.form("code", "yzm_321!")
				.form("roleType", roleType)
            	.timeout(30000)//超时，毫秒
            	.execute().body();
        return R.ok(result);
	}
}
