/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.xunw.jxjy.sys.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xunw.jxjy.feign.sys.entity.SysOrgRelation;
import com.xunw.jxjy.sys.mapper.SysOrgRelationMapper;
import com.xunw.jxjy.sys.service.SysOrgRelationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 机构关系管理
 *
 * <AUTHOR>
 * @date 2019-05-15 15:55:41
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysOrgRelationServiceImpl extends ServiceImpl<SysOrgRelationMapper, SysOrgRelation>
		implements SysOrgRelationService {

	/**
	 * 根据主办单位ID查询关联的机构列表
	 * @param hostOrgId 主办单位ID
	 * @return 机构关系列表
	 */
	@Override
	public List<SysOrgRelation> getByHostOrgId(String hostOrgId) {
		if (StrUtil.isBlank(hostOrgId)) {
			return new ArrayList<>();
		}
		return baseMapper.selectByHostOrgId(hostOrgId);
	}

	/**
	 * 根据机构ID查询关联的主办单位列表
	 * @param orgId 机构ID
	 * @return 机构关系列表
	 */
	@Override
	public List<SysOrgRelation> getByOrgId(String orgId) {
		if (StrUtil.isBlank(orgId)) {
			return new ArrayList<>();
		}
		return baseMapper.selectByOrgId(orgId);
	}

	/**
	 * 根据主办单位ID和机构ID查询关系
	 * @param hostOrgId 主办单位ID
	 * @param orgId 机构ID
	 * @return 机构关系
	 */
	@Override
	public List<SysOrgRelation> getByHostOrgIdAndOrgId(String hostOrgId, String orgId) {
		if (StrUtil.isBlank(hostOrgId) || StrUtil.isBlank(orgId)) {
			return new ArrayList<SysOrgRelation>();
		}
		return baseMapper.selectByHostOrgIdAndOrgId(hostOrgId, orgId);
	}

	/**
	 * 根据关系类型查询机构关系列表
	 * @param relationType 关系类型
	 * @return 机构关系列表
	 */
	@Override
	public List<SysOrgRelation> getByRelationType(String relationType) {
		if (StrUtil.isBlank(relationType)) {
			return new ArrayList<>();
		}
		return baseMapper.selectByRelationType(relationType);
	}

	/**
     * 建立机构关系
     *
     * @param hostOrgId 主办单位ID
     * @param orgId     机构ID
     */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void createRelation(String hostOrgId, String orgId) {
		if (StrUtil.isBlank(hostOrgId) || StrUtil.isBlank(orgId)) {
			throw new IllegalArgumentException("主办单位ID和机构ID不能为空");
		}

		// 检查关系是否已存在
		List<SysOrgRelation> existRelations = getByHostOrgIdAndOrgId(hostOrgId, orgId);
		if (CollectionUtils.isNotEmpty(existRelations)) {
			throw new IllegalArgumentException("机构关系已存在");
		}

		SysOrgRelation relation = new SysOrgRelation();
		relation.setHostOrgId(hostOrgId);
		relation.setOrgId(orgId);
        this.save(relation);
    }

	/**
	 * 批量建立机构关系
	 * @param hostOrgId 主办单位ID
	 * @param orgIds 机构ID列表
	 * @return 操作结果
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean createBatchRelations(String hostOrgId, List<String> orgIds) {
		if (StrUtil.isBlank(hostOrgId) || orgIds == null || orgIds.isEmpty()) {
			throw new IllegalArgumentException("主办单位ID和机构ID列表不能为空");
		}

		List<SysOrgRelation> relations = new ArrayList<>();
		for (String orgId : orgIds) {
			if (StrUtil.isBlank(orgId)) {
				continue;
			}

			// 检查关系是否已存在
			List<SysOrgRelation> existRelations = getByHostOrgIdAndOrgId(hostOrgId, orgId);
			if (CollectionUtils.isNotEmpty(existRelations)) {
				log.warn("机构关系已存在: hostOrgId={}, orgId={}", hostOrgId, orgId);
				continue;
			}

			SysOrgRelation relation = new SysOrgRelation();
			relation.setHostOrgId(hostOrgId);
			relation.setOrgId(orgId);
			relations.add(relation);
		}

		if (relations.isEmpty()) {
			throw new IllegalArgumentException("没有需要创建的机构关系");
		}

		return saveBatch(relations);
	}

	/**
	 * 删除机构关系
	 * @param hostOrgId 主办单位ID
	 * @param orgId 机构ID
	 * @return 操作结果
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean removeRelation(String hostOrgId, String orgId) {
		if (StrUtil.isBlank(hostOrgId) || StrUtil.isBlank(orgId)) {
			throw new IllegalArgumentException("主办单位ID和机构ID不能为空");
		}
		return remove(Wrappers.<SysOrgRelation>lambdaQuery()
				.eq(SysOrgRelation::getHostOrgId, hostOrgId)
				.eq(SysOrgRelation::getOrgId, orgId));
	}

	/**
	 * 检查机构关系是否存在
	 * @param hostOrgId 主办单位ID
	 * @param orgId 机构ID
	 * @return 是否存在
	 */
	@Override
	public Boolean existsRelation(String hostOrgId, String orgId) {
		if (StrUtil.isBlank(hostOrgId) || StrUtil.isBlank(orgId)) {
			return Boolean.FALSE;
		}

		List<SysOrgRelation> relations = getByHostOrgIdAndOrgId(hostOrgId, orgId);
		return CollectionUtils.isNotEmpty(relations);
	}
}
