/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 *
 */

package com.xunw.jxjy.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xunw.jxjy.feign.sys.entity.SysUserInfo;

import java.util.List;

/**
 * <p>
 * 管理员用户信息表 服务类
 * </p>
 */
public interface SysUserInfoService extends IService<SysUserInfo> {

	/**
	 * 通过用户ID查询用户信息
	 * @param userId 用户ID
	 * @return 用户信息
	 */
	SysUserInfo getByUserId(String userId);

	/**
	 * 保存或更新用户信息
	 * @param sysUserInfo 用户信息
	 * @return 是否成功
	 */
	Boolean saveOrUpdateUserInfo(SysUserInfo sysUserInfo);

	/**
	 * 通过用户ID删除用户信息
	 * @param userId 用户ID
	 * @return 是否成功
	 */
	Boolean removeByUserId(String userId);

	/**
	 * 批量查询用户信息
	 * @param userIds 用户ID列表
	 * @return 用户信息列表
	 */
	List<SysUserInfo> listByUserIds(List<String> userIds);

	/**
	 * 根据教师类型查询用户信息
	 * @param teacherTypeId 教师类型ID
	 * @return 用户信息列表
	 */
	List<SysUserInfo> listByTeacherTypeId(String teacherTypeId);

	/**
	 * 查询门户展示的用户信息
	 * @return 用户信息列表
	 */
	List<SysUserInfo> listPortalShowUsers();
}
