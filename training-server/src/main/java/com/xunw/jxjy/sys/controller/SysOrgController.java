/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.xunw.jxjy.sys.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xunw.cloud.common.core.constant.CacheConstants;
import com.xunw.cloud.common.core.constant.CommonConstants;
import com.xunw.cloud.common.core.exception.BizException;
import com.xunw.cloud.common.core.util.R;
import com.xunw.cloud.common.data.resolver.ParamResolver;
import com.xunw.cloud.common.data.tenant.TenantBroker;
import com.xunw.cloud.common.excel.annotation.ResponseExcel;
import com.xunw.cloud.common.log.annotation.SysLog;
import com.xunw.cloud.common.security.annotation.Inner;
import com.xunw.cloud.common.security.util.SecurityUtils;
import com.xunw.jxjy.feign.sys.constant.RoleType;
import com.xunw.jxjy.feign.sys.entity.SysOrg;
import com.xunw.jxjy.feign.sys.enums.OrgType;
import com.xunw.jxjy.sys.service.SysMenuService;
import com.xunw.jxjy.sys.service.SysOrgService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 租户管理
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tenant")
@Tag(description = "tenant", name = "租户管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SysOrgController {

    private final SysOrgService sysOrgService;

    private final SysMenuService sysMenuService;

    /**
     * 分页查询
     *
     * @param page      分页对象
     * @param sysTenant 租户
     * @return 分页对象
     */
    @GetMapping("/page")
    public R getSysTenantPage(@ParameterObject Page page, @ParameterObject SysOrg sysTenant) {
        String currentOrgId = SecurityUtils.getTenantId();
        if (StringUtils.isBlank(sysTenant.getParentId())) {
            if (Objects.equals(currentOrgId, CommonConstants.TENANT_ID_1)) {
                sysTenant.setParentId(null);
                sysTenant.setIsParent(CommonConstants.STR_ONE);
            } else {
                SysOrg currentOrg = sysOrgService.getById(SecurityUtils.getTenantId());
                SysOrg topOrg = sysOrgService.getTopOrg(SecurityUtils.getTenantId());
                OrgType loginOrgType = currentOrg.getOrgType();
                if (sysTenant.getOrgType() == OrgType.HOST_ORG) {
                    if (loginOrgType == OrgType.HOST_ORG) {
                        sysTenant.setParentId(topOrg.getId());
                    } else if (loginOrgType == OrgType.ORG) {
                        List<SysOrg> orgs = sysOrgService.getRelatedHostOrgByOrgId(topOrg.getId());
                        sysTenant.setParentId(orgs.get(0).getId());
                    } else if (loginOrgType == OrgType.ENTRUST_ORG) {
                        List<SysOrg> orgs = sysOrgService.getRelatedHostOrgByOrgId(topOrg.getId());
                        sysTenant.setParentId(orgs.get(0).getId());
                    }
                } else if (sysTenant.getOrgType() == OrgType.ORG) {
                    if (loginOrgType == OrgType.HOST_ORG) {
                        sysTenant.setHostOrgId(topOrg.getId());
                    } else if (loginOrgType == OrgType.ORG) {
                        sysTenant.setParentId(topOrg.getId());
                    }
                } else if (sysTenant.getOrgType() == OrgType.ENTRUST_ORG) {
                    if (loginOrgType == OrgType.HOST_ORG) {
                        sysTenant.setId(topOrg.getId());
                    } else if (loginOrgType == OrgType.ENTRUST_ORG) {
                        sysTenant.setParentId(topOrg.getId());
                    }
                }
            }
        }
        return R.ok(sysOrgService.getSysTenantPage(page, sysTenant));
    }

    /**
     * 通过ID 查询租户信息。
     *
     * @param id ID
     * @return R
     */
    @GetMapping("/details/{id}")
    public R getById(@PathVariable String id) {
        return R.ok(sysOrgService.getById(id));
    }

    /**
     * 查询租户信息。
     *
     * @param query 查询条件
     * @return 租户信息
     */
    @GetMapping("/details")
    public R getDetails(@ParameterObject SysOrg query) {
        return R.ok(sysOrgService.getOne(Wrappers.query(query), false));
    }

    /**
     * 新增租户。
     *
     * @param sysTenant 租户
     * @return R
     */
    @SysLog("新增租户")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('sys_systenant_add')")
    @CacheEvict(value = CacheConstants.TENANT_DETAILS, allEntries = true)
    public R save(@RequestBody SysOrg sysTenant) {
        return R.ok(sysOrgService.saveTenant(sysTenant));
    }

    /**
     * 修改租户
     *
     * @param sysTenant 租户
     * @return R
     */
    @SysLog("修改租户")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('sys_systenant_edit')")
    public R updateById(@RequestBody SysOrg sysTenant) {
        return R.ok(sysOrgService.updateTenant(sysTenant));
    }

    /**
     * 通过id删除租户
     * <p>
     * 为了保证安全,这里只删除租户表的数据，不删除基础表中的租户初始化数据。
     *
     * @param ids id 列表
     * @return R
     */
    @SysLog("删除租户")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('sys_systenant_del')")
    @CacheEvict(value = CacheConstants.TENANT_DETAILS, allEntries = true)
    public R removeById(@RequestBody String[] ids) {
        return R.ok(sysOrgService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 查询全部有效的租户
     *
     * @return R<List < SysOrg>>
     */
    @Inner(value = false)
    @GetMapping("/list")
    public R list() {
        List<SysOrg> tenants = sysOrgService.getNormalTenant();
//				.stream()
//				.filter(tenant -> tenant.getStartTime().isBefore(LocalDateTime.now()))
//				.filter(tenant -> tenant.getEndTime().isAfter(LocalDateTime.now()))
//				.collect(Collectors.toList());
        return R.ok(tenants);
    }

    /**
     * 导出excel 表格
     *
     * @param sysTenant 查询条件
     * @param ids       导出ids
     * @return excel文件
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('sys_systenant_export')")
    public List<SysOrg> export(SysOrg sysTenant, String[] ids) {
        return sysOrgService.list(Wrappers.lambdaQuery(sysTenant).in(ArrayUtil.isNotEmpty(ids), SysOrg::getId, ids));
    }

    /**
     * 菜单树
     *
     * @return List<Tree < Long>>
     */
    @GetMapping(value = "/tree/menu")
    public R getTree() {
        String defaultId = ParamResolver.getStr("TENANT_DEFAULT_ID", "1");
        List<Tree<Long>> trees = new ArrayList<>();
        TenantBroker.runAs(defaultId, (id) -> {
            trees.addAll(sysMenuService.treeMenu(null, null, null));
        });
        return R.ok(trees);
    }


    /**
     * 租户用户管理--左边树
     *
     * @return R<Object>
     */
    @GetMapping("/getTenantUserTree")
    public R<Object> getTenantUserTree(SysOrg sysTenant) {
        if (sysTenant.getOrgType() != OrgType.HOST_ORG && sysTenant.getOrgType() != OrgType.ORG && sysTenant.getOrgType() != OrgType.ENTRUST_ORG) {
            throw BizException.withMessage("机构类型错误，只支持主办单位、培训机构、委托单位");
        }
        if (RoleType.findByEnumName(SecurityUtils.getUser().getRoleType()) == RoleType.ADMIN) {
            sysTenant.setParentId(null);
        } else {
            SysOrg currentOrg = sysOrgService.getById(SecurityUtils.getTenantId());
            SysOrg topOrg = sysOrgService.getTopOrg(SecurityUtils.getTenantId());
            OrgType loginOrgType = currentOrg.getOrgType();
            if (sysTenant.getOrgType() == OrgType.HOST_ORG) {
                if (loginOrgType == OrgType.HOST_ORG) {
                    sysTenant.setParentId(topOrg.getId());
                } else if (loginOrgType == OrgType.ORG) {
                    List<SysOrg> orgs = sysOrgService.getRelatedHostOrgByOrgId(topOrg.getId());
                    sysTenant.setParentId(orgs.get(0).getId());
                } else if (loginOrgType == OrgType.ENTRUST_ORG) {
                    List<SysOrg> orgs = sysOrgService.getRelatedHostOrgByOrgId(topOrg.getId());
                    sysTenant.setParentId(orgs.get(0).getId());
                }
            } else if (sysTenant.getOrgType() == OrgType.ORG) {
                if (loginOrgType == OrgType.HOST_ORG) {
                    sysTenant.setHostOrgId(topOrg.getId());
                } else if (loginOrgType == OrgType.ORG) {
                    sysTenant.setParentId(topOrg.getId());
                }
            } else if (sysTenant.getOrgType() == OrgType.ENTRUST_ORG) {
                if (loginOrgType == OrgType.HOST_ORG) {
                    sysTenant.setId(topOrg.getId());
                } else if (loginOrgType == OrgType.ENTRUST_ORG) {
                    sysTenant.setParentId(topOrg.getId());
                }
            }
        }
        return R.ok(sysOrgService.getTenantUserTree(sysTenant));
    }
}
