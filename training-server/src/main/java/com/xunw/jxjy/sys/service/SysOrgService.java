/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.xunw.jxjy.sys.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xunw.cloud.common.core.util.R;
import com.xunw.jxjy.feign.sys.entity.SysOrg;
import com.xunw.jxjy.feign.sys.enums.OrgType;
import com.xunw.jxjy.sys.dto.OrgTreeDto;

import java.util.List;

/**
 * 租户管理
 *
 * <AUTHOR>
 * @date 2019-05-15 15:55:41
 */
public interface SysOrgService extends IService<SysOrg> {

	/**
	 * 获取正常的租户
	 * @return
	 */
	List<SysOrg> getNormalTenant();

	/**
	 * 保存租户
	 * @param sysTenant
	 * @return
	 */
	Boolean saveTenant(SysOrg sysTenant);

	/**
	 * 修改租户
	 * @param sysTenant
	 * @return
	 */
	Boolean updateTenant(SysOrg sysTenant);

	List<OrgTreeDto> getTenantUserTree(SysOrg sysTenant);

	Page getSysTenantPage(Page page, SysOrg sysTenant);

	/**
	 * 培训机构、委托单位 查询关联的主办单位，注意参数orgId 必须是顶级机构
	 * @param id orgId
	 */
	List<SysOrg> getRelatedHostOrgByOrgId(String id);

	/**
	 * 获取最顶层机构
	 * @param tenantId 机构id
	 * @return 机构
	 */
	SysOrg getTopOrg(String tenantId);
}
