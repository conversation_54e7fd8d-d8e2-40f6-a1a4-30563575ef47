package com.xunw.jxjy;

import com.xunw.cloud.common.feign.annotation.EnablePigxFeignClients;
import com.xunw.cloud.common.security.annotation.EnablePigxResourceServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 培训平台 基于微服务低代码平台改造
 */
//===========Security权限模块 必须引入==============
@EnablePigxResourceServer
//===========微服务Feign调用 单体版本无需引入=========
@EnablePigxFeignClients
//===========服务自动发现 单体版本无需引入============
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.xunw"})
public class TrainingWebApplication {

	public static void main(String[] args) {
		SpringApplication.run(TrainingWebApplication.class, args);
	}

}
