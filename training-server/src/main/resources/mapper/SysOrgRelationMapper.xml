<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: lengleng (<EMAIL>)
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xunw.jxjy.sys.mapper.SysOrgRelationMapper">

    <resultMap id="sysOrgRelationMap" type="com.xunw.jxjy.feign.sys.entity.SysOrgRelation">
        <id property="id" column="id"/>
        <result property="hostOrgId" column="host_org_id"/>
        <result property="orgId" column="org_id"/>
    </resultMap>

    <!-- 根据主办单位ID查询关联的机构列表 -->
    <select id="selectByHostOrgId" resultMap="sysOrgRelationMap">
        SELECT *
        FROM sys_org_relation
        WHERE host_org_id = #{hostOrgId}
    </select>

    <!-- 根据机构ID查询关联的主办单位列表 -->
    <select id="selectByOrgId" resultMap="sysOrgRelationMap">
        SELECT *
        FROM sys_org_relation
        WHERE org_id = #{orgId}
    </select>

    <!-- 根据主办单位ID和机构ID查询关系 -->
    <select id="selectByHostOrgIdAndOrgId" resultMap="sysOrgRelationMap">
        SELECT *
        FROM sys_org_relation
        WHERE host_org_id = #{hostOrgId}
          AND org_id = #{orgId}
    </select>

    <!-- 根据关系类型查询机构关系列表 -->
    <select id="selectByRelationType" resultMap="sysOrgRelationMap">
        SELECT *
        FROM sys_org_relation
    </select>

</mapper>
