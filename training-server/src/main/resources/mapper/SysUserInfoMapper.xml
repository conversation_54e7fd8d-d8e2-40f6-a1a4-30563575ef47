<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~    Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without
  ~ modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~ this list of conditions and the following disclaimer.
  ~ Redistributions in binary form must reproduce the above copyright
  ~ notice, this list of conditions and the following disclaimer in the
  ~ documentation and/or other materials provided with the distribution.
  ~ Neither the name of the pig4cloud.com developer nor the names of its
  ~ contributors may be used to endorse or promote products derived from
  ~ this software without specific prior written permission.
  ~  Author: lengleng (<EMAIL>)
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xunw.jxjy.sys.mapper.SysUserInfoMapper">

	<resultMap id="sysUserInfoMap" type="com.xunw.jxjy.feign.sys.entity.SysUserInfo">
		<id property="id" column="ID"/>
		<result property="userId" column="USER_ID"/>
		<result property="zw" column="ZW"/>
		<result property="brief" column="BRIEF"/>
		<result property="photo" column="PHOTO"/>
		<result property="updatorId" column="UPDATOR_ID"/>
		<result property="updateTime" column="UPDATE_TIME"/>
		<result property="zc" column="ZC"/>
		<result property="gw" column="GW"/>
		<result property="createTime" column="CREATE_TIME"/>
		<result property="education" column="EDUCATION"/>
		<result property="creatorId" column="CREATOR_ID"/>
		<result property="gender" column="GENDER"/>
		<result property="workUnit" column="WORK_UNIT"/>
		<result property="studyDirection" column="STUDY_DIRECTION"/>
		<result property="courses" column="COURSES"/>
		<result property="officeTel" column="OFFICE_TEL"/>
		<result property="remark" column="REMARK"/>
		<result property="isOut" column="IS_OUT"/>
		<result property="recommend" column="RECOMMEND"/>
		<result property="customOne" column="CUSTOM_ONE"/>
		<result property="customTwo" column="CUSTOM_TWO"/>
		<result property="customThree" column="CUSTOM_THREE"/>
		<result property="isShowPortal" column="IS_SHOW_PORTAL"/>
		<result property="email" column="EMAIL"/>
		<result property="zcPhoto" column="ZC_PHOTO"/>
		<result property="bankCardPhoto" column="BANK_CARD_PHOTO"/>
		<result property="teacherTypeId" column="TEACHER_TYPE_ID"/>
		<result property="specialityType" column="SPECIALITY_TYPE"/>
		<result property="professionIds" column="PROFESSION_IDS"/>
		<result property="bankCardNo" column="BANK_CARD_NO"/>
		<result property="bank" column="BANK"/>
		<result property="category" column="CATEGORY"/>
	</resultMap>

	<!-- 通过用户ID查询用户信息 -->
	<select id="selectByUserId" resultMap="sysUserInfoMap">
		SELECT *
		FROM SYS_USER_INFO
		WHERE USER_ID = #{userId}
	</select>

	<!-- 批量查询用户信息 -->
	<select id="selectByUserIds" resultMap="sysUserInfoMap">
		SELECT *
		FROM SYS_USER_INFO
		WHERE USER_ID IN
		<foreach collection="userIds" item="userId" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</select>

	<!-- 根据教师类型查询用户信息 -->
	<select id="selectByTeacherTypeId" resultMap="sysUserInfoMap">
		SELECT *
		FROM SYS_USER_INFO
		WHERE TEACHER_TYPE_ID = #{teacherTypeId}
	</select>

	<!-- 查询门户展示的用户信息 -->
	<select id="selectPortalShowUsers" resultMap="sysUserInfoMap">
		SELECT *
		FROM SYS_USER_INFO
		WHERE IS_SHOW_PORTAL = '1'
		ORDER BY RECOMMEND DESC, CREATE_TIME DESC
	</select>

</mapper>
