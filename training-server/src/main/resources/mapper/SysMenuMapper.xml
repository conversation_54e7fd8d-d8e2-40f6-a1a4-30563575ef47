<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: lengleng (<EMAIL>)
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.sys.mapper.SysMenuMapper">
	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.xunw.jxjy.feign.sys.entity.SysMenu">
		<id column="menu_id" property="menuId"/>
		<result column="name" property="name"/>
		<result column="permission" property="permission"/>
		<result column="path" property="path"/>
		<result column="parent_id" property="parentId"/>
		<result column="icon" property="icon"/>
		<result column="sort_order" property="sortOrder"/>
		<result column="menu_type" property="menuType"/>
		<result column="keep_alive" property="keepAlive"/>
		<result column="visible" property="visible"/>
		<result column="create_time" property="createTime"/>
		<result column="update_time" property="updateTime"/>
		<result column="del_flag" property="delFlag"/>
		<result column="embedded" property="embedded"/>
		<result column="visible" property="visible"/>
	</resultMap>

	<!--通过角色查询菜单信息-->
	<select id="listMenusByRoleId" resultMap="BaseResultMap">
		SELECT 
		   sys_menu.menu_id,
		   sys_menu.name,
		   sys_menu.permission,
		   sys_menu.path,
		   sys_menu.parent_id,
		   sys_menu.icon,
		   sys_menu.sort_order,
		   sys_menu.keep_alive,
		   sys_menu.menu_type,
		   sys_menu.create_time,
		   sys_menu.update_time,
		   sys_menu.del_flag,
		   sys_menu.embedded,
		   sys_menu.visible
		FROM 
		    sys_menu
		LEFT JOIN sys_role_menu ON sys_menu.menu_id = sys_role_menu.menu_id
		WHERE sys_menu.del_flag = '0'
			and sys_role_menu.org_id is null
		<if test="roleId != null">
			AND sys_role_menu.role_id = #{roleId}
		</if>
		ORDER BY sys_menu.sort_order DESC
	</select>

	<!--通过角色ID 查询权限-->
	<select id="listPermissionsByRoleIds" resultType="java.lang.String">
		SELECT m.permission
		FROM sys_menu m,
			 sys_role_menu rm
		WHERE m.menu_id = rm.menu_id
		  AND m.del_flag = '0'
		  AND rm.role_id IN (#{roleIds})
	</select>

	<select id="listMenusByRoleIdAndOrgId" resultMap="BaseResultMap">
		SELECT
			sys_menu.menu_id,
			sys_menu.name,
			sys_menu.permission,
			sys_menu.path,
			sys_menu.parent_id,
			sys_menu.icon,
			sys_menu.sort_order,
			sys_menu.keep_alive,
			sys_menu.menu_type,
			sys_menu.create_time,
			sys_menu.update_time,
			sys_menu.del_flag,
			sys_menu.embedded,
			sys_menu.visible
		FROM
			sys_menu
		LEFT JOIN sys_role_menu ON sys_menu.menu_id = sys_role_menu.menu_id
		WHERE sys_menu.del_flag = '0'
		<if test="roleId != null">
			AND sys_role_menu.role_id = #{roleId}
		</if>
		<if test="orgId != null">
			AND sys_role_menu.org_id = #{orgId}
		</if>
		ORDER BY sys_menu.sort_order DESC
	</select>
</mapper>
