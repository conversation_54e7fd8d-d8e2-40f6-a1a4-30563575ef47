<?xml version="1.0" encoding="UTF-8"?>


<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.sys.mapper.SysDeptMapper">
    
    <insert id="insertSchoolAndDept">
        insert into sys_school_dept(school_id,dept_id)
        values
        <foreach collection="ids" item="id" separator=",">
            (#{id},#{deptId})
        </foreach>
    </insert>
    
    <insert id="insertPartnerAndDept">
        insert into sys_parent_dept(partner_id,dept_id)
        values
        <foreach collection="ids" item="id" separator=",">
        (#{id},#{deptId})
        </foreach>
    </insert>

    <select id="getPartnerGroups" resultType="com.xunw.jxjy.feign.sys.entity.SysDept">
        SELECT
            *
        FROM
            sys_dept a
        WHERE
            EXISTS (
                SELECT
                    s.*
                FROM
                    sys_parent_dept s
                        LEFT JOIN sys_dept p ON p.dept_id = s.partner_id
                WHERE
                    p.parent_id = 0
                  AND s.dept_id = #{parentDeptId}
                  AND a.dept_id = s.partner_id)
    </select>
</mapper>
