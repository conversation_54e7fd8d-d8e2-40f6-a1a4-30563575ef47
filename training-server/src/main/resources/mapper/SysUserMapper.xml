<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: lengleng (<EMAIL>)
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.sys.mapper.SysUserMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.xunw.jxjy.feign.sys.vo.UserVO">
        <id column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="salt" property="salt"/>
        <result column="phone" property="phone"/>
        <result column="avatar" property="avatar"/>
        <result column="wx_openid" property="wxOpenid"/>
        <result column="qq_openid" property="qqOpenid"/>
        <result column="gitee_login" property="giteeOpenId"/>
        <result column="osc_id" property="oscOpenId"/>
        <result column="ucreate_time" property="createTime"/>
        <result column="uupdate_time" property="updateTime"/>
        <result column="lock_flag" property="lockFlag"/>
        <result column="udel_flag" property="delFlag"/>
        <result column="dept_id" property="deptId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="dept_name" property="deptName"/>
        <result column="nickname" property="nickname"/>
        <result column="name" property="name"/>
        <result column="email" property="email"/>
        <collection property="roleList" ofType="com.xunw.jxjy.feign.sys.entity.SysRole"
                    select="com.xunw.jxjy.sys.mapper.SysRoleMapper.listRolesByUserId" column="user_id">
        </collection>
        <collection property="postList" ofType="com.xunw.jxjy.feign.sys.entity.SysPost"
                    select="com.xunw.jxjy.sys.mapper.SysPostMapper.listPostsByUserId" column="user_id">
        </collection>
    </resultMap>


    <resultMap id="baseMap" type="com.xunw.jxjy.feign.sys.vo.UserVO">
        <id column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="salt" property="salt"/>
        <result column="phone" property="phone"/>
        <result column="avatar" property="avatar"/>
        <result column="wx_openid" property="wxOpenid"/>
        <result column="qq_openid" property="qqOpenid"/>
        <result column="gitee_login" property="giteeOpenId"/>
        <result column="osc_id" property="oscOpenId"/>
        <result column="ucreate_time" property="createTime"/>
        <result column="uupdate_time" property="updateTime"/>
        <result column="lock_flag" property="lockFlag"/>
        <result column="udel_flag" property="delFlag"/>
        <result column="dept_id" property="deptId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="dept_name" property="deptName"/>
        <result column="nickname" property="nickname"/>
        <result column="name" property="name"/>
        <result column="email" property="email"/>
    </resultMap>


    <sql id="userRoleSql">
        u.user_id,
        u.username,
        u.password,
        u.salt,
        u.mobile phone,
        u.sfzh,
        u.avatar,
        u.wx_openid,
        u.qq_openid,
        u.dept_id,
        u.del_flag,
        u.status lock_flag,
        u.org_id tenant_id,
        u.create_by,
        u.create_time  ucreate_time,
        u.update_time  uupdate_time,
        r.role_id,
        r.role_name,
        r.role_code,
        r.role_desc,
        r.create_time  rcreate_time,
        r.update_time  rupdate_time
    </sql>

    <sql id="userRoleDeptSql">
        u.user_id,
        u.username,
        u.password,
        u.salt,
        u.mobile phone,
        u.sfzh,
        u.avatar,
        u.wx_openid,
        u.qq_openid,
        u.gitee_login,
        u.osc_id,
        u.del_flag,
        u.status lock_flag,
        u.org_id tenant_id,
        u.nickname,
        u.name,
        u.create_by,
        u.create_time  ucreate_time,
        u.update_time  uupdate_time,
        d.name  dept_name,
        d.dept_id
    </sql>

    <select id="getUserVoByUsername" resultMap="baseResultMap">
        SELECT
            u.*
        FROM
            sys_user u
        WHERE 
            u.username = #{username}  
          and 
            u.del_flag = '0'
    </select>

    <select id="getUserVoById" resultMap="baseResultMap">
        SELECT
            <include refid="userRoleDeptSql"/>,
            ui.*
        FROM
            sys_user u
        left join sys_user_info ui on ui.USER_ID = u.id
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = '0'
        WHERE
            u.user_id = #{id} 
          and 
            u.del_flag = '0'
    </select>

    <select id="getUserVosPage" resultMap="baseResultMap">
        SELECT
            u.user_id,
            u.username,
            u.password,
            u.salt,
            u.MOBILE phone,
            u.avatar,
            u.wx_openid,
            u.qq_openid,
            u.dept_id,
            u.create_by,
            u.create_time ucreate_time,
            u.update_time uupdate_time,
            u.del_flag,
            u.lock_flag,
            u.STATUS,
            u.ORG_ID tenant_id,
            u.nickname,
            u.name,
            u.email,
            d.name dept_name
        FROM
            sys_user u
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        <where>
            u.del_flag = '0'
            <if test="query.username != null and query.username != ''">
                <bind name="usernameLike" value="'%'+query.username+'%'"/>
                AND u.username LIKE #{usernameLike}
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND u.dept_id = #{query.deptId}
            </if>
            <if test="query.phone != null and query.phone != ''">
                <bind name="phoneLike" value="'%'+query.phone+'%'"/>
                AND u.mobile LIKE #{phoneLike}
            </if>
            <if test="query.roleType != null and query.roleType != ''">
                and exists (
                        select 
                            * 
                        from 
                            sys_user_role ur left join sys_role r on ur.role_id = r.role_id 
                        where 
                            ur.user_id = u.user_id 
                        and 
                            r.role_code = #{query.roleType}
                )
            </if>
            <if test="query.orgId != null and query.orgId != ''">
                and u.org_id = #{query.orgId}
            </if>
            <if test="query.tenantId != null and query.tenantId != ''">
                and u.org_id = #{query.tenantId}
            </if>
        </where>

        <if test="_databaseId != 'mssql'">
            ORDER BY u.create_time DESC
        </if>
    </select>

    <select id="selectVoListByScope" resultMap="baseResultMap">
        SELECT
            u.user_id,
            u.username,
            u.password,
            u.salt,
            u.MOBILE phone,
            u.avatar,
            u.wx_openid,
            u.qq_openid,
            u.dept_id,
            u.create_by,
            u.create_time ucreate_time,
            u.update_time uupdate_time,
            u.del_flag,
            u.STATUS lock_flag,
            u.ORG_ID tenant_id,
            u.nickname,
            u.name,
            u.email,
            d.name dept_name
        FROM
            sys_user u
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
        <where>
            u.del_flag = '0'
            <if test="query.username != null and query.username != ''">
                <bind name="usernameLike" value="'%'+query.username+'%'"/>
                AND u.username LIKE #{usernameLike}
            </if>
            <if test="query.phone != null and query.phone != ''">
                <bind name="phoneLike" value="'%'+query.phone+'%'"/>
                AND u.MOBILE LIKE #{phoneLike}
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND u.dept_id = #{query.deptId}
            </if>
            <if test="ids != null">
                AND u.user_id in
                <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.roleType != null and query.roleType != ''">
                and exists (
                        select 
                            * 
                        from 
                            sys_user_role ur left join sys_role r on ur.role_id = r.role_id 
                        where 
                            ur.user_id = u.user_id 
                        and 
                            r.role_code = #{query.roleType}
                )
            </if>
            <if test="query.orgId != null">
                and u.ORG_ID = #{query.orgId}
            </if>
        </where>

        <if test="_databaseId != 'mssql'">
            ORDER BY u.create_time DESC
        </if>
    </select>

    <select id="getCompanyUserList" resultType="Map">
        SELECT
            *
        FROM
            s_user
        WHERE
            GROUP_ID = #{loginOrgId}
           OR EXISTS (
            SELECT
                *
            FROM
                ap_s_group a
            WHERE
                ROLE_TYPE = "COMPANY"
              AND a.PARENT_GROUPID = #{loginOrgId}
              AND a.ID = GROUP_ID
        )
    </select>
    <select id="selectUserVoList" resultMap="baseMap">
        SELECT
        <include refid="userRoleDeptSql"/>
        FROM
            sys_user u
        left join sys_user_info ui on ui.USER_ID = u.USER_ID
        LEFT JOIN sys_dept d ON d.dept_id = u.dept_id and d.del_flag = '0'
        WHERE u.del_flag = '0'
    </select>

    <select id="findByUserId" resultType="com.xunw.jxjy.feign.sys.entity.SysUser">
        SELECT
            u.*
        FROM
            sys_user u
        WHERE
            u.user_id = #{userId}
    </select>
</mapper>
