<?xml version="1.0" encoding="UTF-8"?>


<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.jxjy.sys.mapper.SysOrgMapper">

    <select id="checkTenant" resultType="java.lang.Long">
        select sum(a.con)
        from (
              select 
                  count(*) con
              from 
                  zy_b_specialty_plan
              where 
                  org_groupid = #{id}
              union all
              select 
                  count(*) con
              from 
                  bm_b_student_register_info
              where 
                  org_id = #{id}
              union all
              select 
                  count(*) con
              from 
                  b_student_register_info
              where 
                  ORG_GROUPID = #{id}
              ) a
    </select>
    
    <select id="getCompanyUserList" resultType="java.util.Map">
        SELECT
            u.*
        FROM
            sys_user u
        INNER JOIN sys_dept t ON u.dept_id = t.dept_id
        AND u.tenant_id = t.tenant_id
        WHERE
            t.ROLE_TYPE = 'COMPANY'
          AND 
            u.tenant_id = #{tenantId}
          AND 
            u.del_flag = 0
    </select>

    <select id="getSysTenantPage" resultType="com.xunw.jxjy.feign.sys.entity.SysOrg">
        select
            g.*
        from
        sys_org g
        <where>
            <if test="query.orgType != null">
                g.org_type = #{query.orgType}
            </if>
            <!-- 根据主办单位查询培训机构、委托单位 -->
            <if test="query.hostOrgId != null and query.hostOrgId !=''">
                and exists(select 1 from sys_org_relation sr where sr.host_org_id=#{query.hostOrgId} and sr.org_id = g.id)
            </if>
            <if test="query.parentId != null and query.parentId !=''">
                and g.parent_id = #{query.parentId}
            </if>
            <if test="query.isParent != null and query.isParent !=''">
                and g.is_parent = #{query.isParent}
            </if>
            <if test="query.keyword!=null and query.keyword!=''">
                and (g.code like '%'||#{query.keyword}||'%' or g.name like '%'||#{query.keyword}||'%')
            </if>
            <if test="query.isCollege!=null and query.isCollege!=''">
                and g.is_college = #{query.isCollege}
            </if>
        </where>
        order by g.create_time desc, g.code asc
    </select>

    <select id="getRelatedHostOrgByOrgId" resultType="com.xunw.jxjy.feign.sys.entity.SysOrg">
        select
            g.*
        from
            sys_org_relation r
        inner join sys_org g on r.host_org_id = g.id
        where r.org_id = #{orgId}
        order by g.code asc
    </select>

    <select id="treeList" resultType="com.xunw.jxjy.feign.sys.entity.SysOrg">
        select
            g.*
        from
            sys_org g
        <where>
            <if test="query.orgType != null">
                g.org_type = #{query.orgType}
            </if>
            <!-- 根据主办单位递归查询培训机构、委托单位及其子部门 -->
            <if test="query.hostOrgId != null and query.hostOrgId !=''">
                and exists(select 1 from sys_org a where a.id = g.id start with exists (
                select 1 from sys_org_relation sr where sr.host_org_id=#{query.hostOrgId} and sr.org_id = a.id)
                connect by prior a.id = a.parent_id)
            </if>
            <!-- 根据父机构递归查询子部门 -->
            <if test="query.parentId != null and query.parentId !=''">
                and exists(select 1 from sys_org a where a.id = g.id start with a.id=#{query.parentId} connect by prior a.id = a.parent_id)
            </if>
        </where>
        order by g.create_time desc, g.code asc
    </select>

    <select id="getTopOrg" resultType="com.xunw.jxjy.feign.sys.entity.SysOrg">
        select
            g.*
        from
            sys_org g
        where g.id in(
        select g.id from sys_org g start with g.id = #{orgId} connect by g.id = prior g.parent_id
        ) and g.is_parent = 1
    </select>
</mapper>
