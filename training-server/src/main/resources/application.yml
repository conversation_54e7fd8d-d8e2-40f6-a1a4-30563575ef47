server:
  port: 6999
# 前端密码登录解密密钥
gateway:
  encodeKey: pigxpigxpigxpigx

spring:
  application:
    name: training-service
  jackson:
    default-property-inclusion: non_null
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      username: nacos
      password: nacos
      discovery:
        server-addr: ${NACOS_HOST:**************}:${NACOS_PORT:8848}
        namespace: ${NACOS_NAMESPACE:micro_test}
  redis:
    host: **************
    password: sebms_study
    database: 3
    port: 6379
    timeout: 180000
    pool:
      max-active: 500
      max-wait: -1
      max-idle: 100
      min-idle: 20
  datasource:
    dynamic:
      primary: oracle
      strict: false
      datasource:
        oracle:
          driverClassName: oracle.jdbc.OracleDriver
          url: ******************************************
          username: TRAINING_V4
          password: WNZ
          druid:
            proxyFilters: sqlLogFilter #打印完整SQL语句
#        mysql:
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: jdbc:mysql://${MYSQL_HOST:**************}:${MYSQL_PORT:5386}/${MYSQL_DB:training_v4}?characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowMultiQueries=true
#          username: ${MYSQL_USER:root}
#          password: ${MYSQL_PWD:xunw2020}
#          druid:
#            proxyFilters: sqlLogFilter #打印完整SQL语句
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      stat-view-servlet:
        enabled: true
        allow: ""
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 10000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

# 端点对外暴露
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    restart:
      enabled: true
    health:
      show-details: ALWAYS
# mybatis-plus 配置
mybatis-plus:
  tenant-enable: ture
  mapper-locations: classpath:/mapper/**/*Mapper.xml
  type-enums-package: com.xunw.jxjy.**.enums
  global-config:
    capitalMode: true
    banner: false
    db-config:
      id-type: auto
      select-strategy: not_empty
      insert-strategy: not_empty
      update-strategy: not_null
  type-handlers-package:  com.xunw.cloud.common.data.handler
  configuration:
    jdbc-type-for-null: 'null'
    call-setters-on-nulls: true
    shrink-whitespaces-in-sql: true
mybatis-plus-join:
  banner: false #关闭连表查询组件banner
sso:
  private_key: MIICeQIBADANBgkqhkiG9w0BAQEFAASCAmMwggJfAgEAAoGBAPfsJ6vXYRnIksZNf+k6yBdm45oFr5G/bkciOSv49sTkLHMcDMvhMkD1U+xo9hSd3tZu7fVfQTtX8H3H8yn5gOT4xNKdqdw9kRXWeHnfaIRSlNuvHLcmcFoxUUwOeOWuqwzDIVSrZHghVQjpm7j8ZPRqE4BukrbljQ1+oZKBuT1/AgMBAAECgYEAiQWXI21lODfXjwv0PvYC8pQmBNFRqK7xhxUOHhR6ZnVpb7E4DVKIUMiGOqoEH37lUwR6NdwQlHjU7N+esDs6lbu5xdvCf/N+4vOq8m8gp18BsJkg+MfTE5VH/SVTXJVTyZhULwP85JQlXAfpS0TTZZ32zsT3gzeDKJNh1nKW9JkCQQD/07Tptm31NyhJ0paAkGi8oaizKlsNJyTf7cGOXXhTXDDbhWnXwimWoUUSuZhOKFrkqKLpGwEIurQU4qwz5n2tAkEA+BcUZ7V2iapOVLUq+RqKc9Ob9lvzwmkYTzjZVOJpmU2xgS9nc119z3qJURT6iQcLX2A+keI0nzzqcF2AIxT1WwJBAOtwUP0OEaAthPx7vFrRjDKdrH5HKsiuD7euPgXha817+NLus3wHivK/tKwI9mxno07xDAzv90GrYfQesFCLfXkCQQDBfe6yPDtpf2R8eJg9lM+rKr2XVXw2gpUtJJ+MrtHBeXEZ2okK1z4Klf2HOZ91RANAreXfDdgQ4gfO/32y3uPFAkEA+R+0mbU2w4Ka1cOW1a5EenVrdjpAemTsyW3V0ZLfUC5ayqH1/sL4OQhAF9Ybb7WNjaLHTBVWhkjd9++jYQ1u5w==
  public_key: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQD37Cer12EZyJLGTX/pOsgXZuOaBa+Rv25HIjkr+PbE5CxzHAzL4TJA9VPsaPYUnd7Wbu31X0E7V/B9x/Mp+YDk+MTSnancPZEV1nh532iEUpTbrxy3JnBaMVFMDnjlrqsMwyFUq2R4IVUI6Zu4/GT0ahOAbpK25Y0NfqGSgbk9fwIDAQAB
  sebms_url: https://test.whxunw.com/examtest
# 配置文件加密根密码
jasypt:
  encryptor:
    password: pigx
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
## spring security 配置
security:
  oauth2:
    client:
      ignore-urls:
        - /webjars/**
        - /doc.html
        - /token/check_token
        - /error
        - /druid/**
        - /actuator/**
        - /code/**
        - /student/**

# 本地文件系统
file:
  local:
    enable: true
    basePath: /Users/<USER>/Downloads/files
# 七牛云文件系统
  bucketName: jxjy-study
  oss:
    enable: true
    endpoint: s3.cn-east-1.qiniucs.com
    access-key: eO32A7yxJWpO6BAu65L4sykwUTYRfshEZ2uWhDod
    secret-key: yyJWIpNz6kgbYLrtnoQhUb2g4mXcMh6Nzsx77cab
    customDomain: https://jxjy-att.whxunw.com/

# 租户表维护
pigx:
  mybatis:
    showSql: true #是否打印完整SQL语句
  tenant:
    column: tenant_id
    tables:
      - sys_log
      - sys_social_details
      - sys_log
      - sys_file
      - sys_file_group
      - sys_post
      - sys_message
      - sys_message_relation

# seata配置
seata:
  enabled: true
  enable-auto-data-source-proxy: true
  tx-service-group: zjxx_tx_group
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: ${NACOS_HOST:**************}:${NACOS_PORT:8848}
      username: nacos
      password: nacos
      group: DEFAULT_GROUP
      namespace: ${NACOS_NAMESPACE:micro_test}
  config:
    type: file
  #    nacos:
  #      server-addr: localhost:8848
  #      group: SEATA_GROUP
  #      username: nacos
  #      password: nacos
  #      namespace: e5a9f3fc-cfd5-48d9-9f80-a5fb9ede58d3
  service:
    vgroup-mapping:
      zjxx_tx_group: default
    disable-global-transaction: false
  client:
    rm:
      report-success-enable: false
task:
  corePoolSize: 1
  maxPoolSize: 3
  queueCapacity: 200

# 验证码配置
aj:
  captcha:
    cache-type: redis
    water-mark: www.whxunw.com
# 短信配置
sms:
  key: KMaYrOIiFV6PgCSf
  secret: xiFKt2cUzVJapyPFquaLuFzDH8AI5EmE

qiniu:
  access-Key: eO32A7yxJWpO6BAu65L4sykwUTYRfshEZ2uWhDod
  bucket: jxjy-study
  imageMogr2-rotate: ?imageMogr2/rotate/
  secret-key: yyJWIpNz6kgbYLrtnoQhUb2g4mXcMh6Nzsx77cab
  urlprefix: https://jxjy-att.whxunw.com/
  zbhfprefix: JXJY

att:
  tempdir: /opt/org_jxjy/temp
  store-type: CLOUD
  store-dir: /opt/org_jxjy/temp
  root-dir: sebms_att
  url-prefix: https://jxjy-att.whxunw.com/
  ext-type: .mp3;.mp4;.wmv;.rmvb;.flv;.mpeg;.wav
  path-contract: https://jxjy-att.whxunw.com/sebms_att/upload/editor/20231128/9D4A31C76673499192E34A857733448F.pdf
  pdf-path: /opt/org_jxjy/atts

