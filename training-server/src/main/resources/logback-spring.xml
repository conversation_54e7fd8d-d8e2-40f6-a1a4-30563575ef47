<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">

	<property name="log.path" value="logs/${project.artifactId}.log"/>

	<!-- 彩色日志格式 -->
	<property name="CONSOLE_LOG_PATTERN"
			  value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
	<!-- 彩色日志依赖的渲染类 -->
	<conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
	<conversionRule conversionWord="wex"
					converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
	<conversionRule conversionWord="wEx"
					converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
	
	<!-- Console log output -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>${CONSOLE_LOG_PATTERN}</pattern>
		</encoder>
	</appender>

	<!-- Log file  -->
	<!--按天生成日志 -->
	<appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${log.path}</file>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}| %thread | %level | %X{TRACE_ID}
				- %X{KEY} | %m | [%class:%line]%n</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<rollingPolicy
			class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${log.path}.%d{yyyy-MM-dd}.%i.log
			</fileNamePattern>
			<maxHistory>100</maxHistory>
			<maxFileSize>100MB</maxFileSize>
		</rollingPolicy>
	</appender>

	<!--nacos 心跳 INFO 屏蔽-->
	<logger name="com.alibaba.nacos" level="OFF">
		<appender-ref ref="file"/>
	</logger>

	<!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
	<root level="INFO">
		<appender-ref ref="console"/>
		<appender-ref ref="file"/>
	</root>
</configuration>
